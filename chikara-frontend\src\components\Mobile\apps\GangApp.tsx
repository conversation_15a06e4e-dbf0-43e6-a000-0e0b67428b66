import React from 'react';
import { motion } from 'framer-motion';
import { Users, Trophy, Target, AlertCircle, TrendingUp, Shield } from 'lucide-react';

const GangApp: React.FC = () => {
  const gangInfo = {
    name: 'Shadow Dragons',
    rank: 3,
    members: 24,
    territory: 'Shibuya District',
    reputation: 8750,
    warStatus: 'At War',
  };

  const members = [
    { id: '1', name: 'Dragon Master', role: 'Leader', level: 45, online: true },
    { id: '2', name: 'Silent Blade', role: 'Lieutenant', level: 38, online: true },
    { id: '3', name: 'Iron Fist', role: 'Enforcer', level: 32, online: false },
    { id: '4', name: 'Swift Shadow', role: 'Scout', level: 28, online: true },
  ];

  const activities = [
    { id: '1', text: 'Territory defended against Red Tigers', time: '2 hours ago', type: 'defense' },
    { id: '2', text: 'New member joined: Steel Warrior', time: '5 hours ago', type: 'member' },
    { id: '3', text: 'Gang war declared on Blue Serpents', time: '1 day ago', type: 'war' },
  ];

  return (
    <div className="h-full bg-gray-900 overflow-y-auto">
      {/* Gang Header */}
      <div className="bg-gradient-to-r from-purple-900 to-purple-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-2xl font-bold text-white">{gangInfo.name}</h2>
            <p className="text-purple-200">Rank #{gangInfo.rank} • {gangInfo.territory}</p>
          </div>
          <div className="text-center">
            <Trophy className="w-8 h-8 text-yellow-400 mx-auto mb-1" />
            <p className="text-yellow-400 font-bold">{gangInfo.reputation}</p>
            <p className="text-xs text-purple-200">Reputation</p>
          </div>
        </div>
        
        {gangInfo.warStatus && (
          <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-3 flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <span className="text-red-300 text-sm font-medium">Gang War Active</span>
          </div>
        )}
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-3 gap-3 p-4">
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="bg-gray-800 rounded-lg p-3 text-center"
        >
          <Users className="w-6 h-6 text-purple-400 mx-auto mb-1" />
          <p className="text-white font-bold">{gangInfo.members}</p>
          <p className="text-xs text-gray-400">Members</p>
        </motion.div>
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="bg-gray-800 rounded-lg p-3 text-center"
        >
          <Target className="w-6 h-6 text-red-400 mx-auto mb-1" />
          <p className="text-white font-bold">12</p>
          <p className="text-xs text-gray-400">Battles Won</p>
        </motion.div>
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="bg-gray-800 rounded-lg p-3 text-center"
        >
          <Shield className="w-6 h-6 text-blue-400 mx-auto mb-1" />
          <p className="text-white font-bold">8</p>
          <p className="text-xs text-gray-400">Territories</p>
        </motion.div>
      </div>

      {/* Online Members */}
      <div className="p-4">
        <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
          <Users className="w-5 h-5 text-purple-400" />
          Online Members ({members.filter(m => m.online).length})
        </h3>
        <div className="space-y-2">
          {members.filter(m => m.online).map((member, index) => (
            <motion.div
              key={member.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              className="bg-gray-800 rounded-lg p-3 flex items-center justify-between"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">{member.name[0]}</span>
                </div>
                <div>
                  <p className="text-white font-medium">{member.name}</p>
                  <p className="text-xs text-gray-400">{member.role} • Level {member.level}</p>
                </div>
              </div>
              <div className="w-2 h-2 bg-green-500 rounded-full" />
            </motion.div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="p-4">
        <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-purple-400" />
          Recent Activity
        </h3>
        <div className="space-y-2">
          {activities.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="bg-gray-800 rounded-lg p-3"
            >
              <p className="text-gray-300 text-sm">{activity.text}</p>
              <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="p-4 space-y-2">
        <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-medium transition-colors">
          Gang Chat
        </button>
        <button className="w-full bg-gray-800 hover:bg-gray-700 text-white py-3 rounded-lg font-medium transition-colors">
          View All Members
        </button>
      </div>
    </div>
  );
};

export default GangApp;
