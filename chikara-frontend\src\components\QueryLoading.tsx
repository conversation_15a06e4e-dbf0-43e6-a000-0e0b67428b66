import React from "react";
import { cn } from "@/lib/utils";

interface QueryLoadingProps {
    className?: string;
    /**
     * Whether the component should show loading state
     */
    isLoading: boolean;
    /**
     * Show a shimmer/skeleton effect instead of spinner
     */
    showSkeleton?: boolean;
    /**
     * Size variant for the loading display
     */
    size?: "sm" | "md" | "lg";
}

function QueryLoading({ className, isLoading, showSkeleton = false, size = "md" }: QueryLoadingProps) {
    // Don't show if not loading
    if (!isLoading) {
        return null;
    }

    const sizeClasses = {
        sm: {
            spinner: "loading-lg",
            skeleton: {
                container: "space-y-2",
                text: "h-3",
                content: "h-24",
                button: "h-6",
            },
        },
        md: {
            spinner: "w-16 h-16",
            skeleton: {
                container: "space-y-3",
                text: "h-4",
                content: "h-32",
                button: "h-8",
            },
        },
        lg: {
            spinner: "w-20 h-20",
            skeleton: {
                container: "space-y-4",
                text: "h-5",
                content: "h-40",
                button: "h-10",
            },
        },
    };

    const sizes = sizeClasses[size];

    if (showSkeleton) {
        return (
            <div className={cn("w-full", className)}>
                <div className={cn(sizes.skeleton.container)}>
                    <div className={cn("skeleton", sizes.skeleton.text, "w-3/4")}></div>
                    <div className={cn("skeleton", sizes.skeleton.text, "w-full")}></div>
                    <div className={cn("skeleton", sizes.skeleton.text, "w-5/6")}></div>
                    <div className={cn("skeleton", sizes.skeleton.content, "w-full", "mt-4")}></div>
                    <div className="flex gap-2 mt-4">
                        <div className={cn("skeleton", sizes.skeleton.button, "flex-1")}></div>
                        <div className={cn("skeleton", sizes.skeleton.button, "flex-1")}></div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={cn("flex items-start justify-center h-full", className)}>
            <div className="flex items-center justify-center w-full" style={{ height: "33%" }}>
                <span className={cn("loading loading-spinner text-primary", sizes.spinner)}></span>
            </div>
        </div>
    );
}

export default QueryLoading;
