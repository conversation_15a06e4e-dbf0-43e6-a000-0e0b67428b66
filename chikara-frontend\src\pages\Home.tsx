// import equipmentIcon from "@/assets/icons/navitems/equipment.png";
import trainingImg from "@/assets/images/UI/Skills/strength.png";
import inventoryIcon from "@/assets/icons/navitems/inventory.png";
import characterIcon from "@/assets/icons/navitems/character.png";
import abilitiesIcon from "@/assets/icons/navitems/abilities.png";
import talentsIcon from "@/assets/icons/navitems/talents.png";
import dailyTasksIcon from "@/assets/icons/navitems/tasksOld.png";
import blue2BG from "@/assets/images/UI/BackgroundImages/blue3.jpg";
import blueBG from "@/assets/images/UI/BackgroundImages/blueBars.jpg";
import pinkBG from "@/assets/images/UI/BackgroundImages/pink2.jpg";
import greenBG from "@/assets/images/UI/BackgroundImages/green1.jpg";
import orangeBG from "@/assets/images/UI/BackgroundImages/orangeBars.jpg";
import purple2BG from "@/assets/images/UI/BackgroundImages/purple2.jpg";
import purpleBG from "@/assets/images/UI/BackgroundImages/purpleBars.jpg";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import Accomodation from "@/features/home/<USER>/Accomodation";
import Updates from "@/features/home/<USER>/Updates";
import { checkLevelGate } from "@/helpers/levelGates";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { GraduationCap, Inbox, Mail, Swords, TrendingUp, Target, Trophy } from "lucide-react";
import { Link } from "react-router-dom";
import Spinner from "@/components/Spinners/Spinner";
import useGetUnreadNotifications from "@/hooks/api/useGetUnreadNotifications";
import useGetUnreadMessages from "@/hooks/api/useGetUnreadMessages";
import news from "@/constants/news";

function Home() {
    const { data: currentUser, isLoading, isError } = useFetchCurrentUser();
    const { data: unreadNotifications } = useGetUnreadNotifications();
    const { data: unreadMessages } = useGetUnreadMessages();

    const userLevel = currentUser?.level ?? 0;
    const talentsGate = checkLevelGate("talents", userLevel);
    const dailyQuestsGate = checkLevelGate("dailyQuests", userLevel);

    // Get the latest 2 news posts
    const latestNews = news.slice(0, 2).map((post) => ({
        id: post.id,
        name: post.title,
        href: post.href ? `/news/${post.href}` : "/news",
        username: "The Headmaster",
        userID: 1,
        date: post.date,
        datetime: new Date(post.date).toISOString().split("T")[0],
    }));

    const mainFeatures = [
        {
            title: "Character",
            subtitle: "Manage your character",
            icon: characterIcon,
            background: purple2BG,
            link: "/character",
            locked: false,
        },
        {
            title: "Inventory",
            subtitle: "Manage your items",
            icon: inventoryIcon,
            background: blueBG,
            link: "/inventory",
            locked: false,
        },
        // {
        //     title: "Equipment",
        //     subtitle: "Gear up for battle",
        //     icon: equipmentIcon,
        //     background: purple2BG,
        //     link: "/equipment",
        //     locked: false,
        // },
        {
            title: "Training",
            subtitle: "Train your stats",
            icon: trainingImg,
            background: orangeBG,
            link: "/training",
            locked: false,
        },
        // {
        //     title: "Story Mode",
        //     subtitle: "Follow your journey",
        //     icon: storyIcon,
        //     background: purpleBG,
        //     link: "/story",
        //     locked: false,
        // },
        {
            title: "Talents",
            subtitle: "Unlock new abilities",
            icon: talentsIcon,
            background: greenBG,
            link: "/talents",
            locked: talentsGate.isLocked,
            lockedLevel: talentsGate.requiredLevel,
            points: currentUser?.talentPoints,
        },
        {
            title: "Abilities",
            subtitle: "Master your craft",
            icon: abilitiesIcon,
            background: pinkBG,
            link: "/abilities",
            locked: talentsGate.isLocked,
            lockedLevel: talentsGate.requiredLevel,
        },
        {
            title: "Daily Tasks",
            subtitle: "Complete challenges",
            icon: dailyTasksIcon,
            background: blue2BG,
            link: "/dailies",
            locked: dailyQuestsGate.isLocked,
            lockedLevel: dailyQuestsGate.requiredLevel,
        },
    ];

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Spinner />
            </div>
        );
    }
    if (isError || !currentUser) {
        return (
            <div className="flex justify-center items-center h-screen">
                <p className="text-red-500">Error loading user data</p>
            </div>
        );
    }

    return (
        <div className="-mx-4 md:-mt-4 flex-1 px-4 md:px-0">
            {/* Training Status Dashboard */}
            <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 py-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {/* Level Progress */}
                    <div className="card bg-base-300 shadow-lg">
                        <div className="card-body p-4">
                            <div className="flex items-center justify-between mb-2">
                                <TrendingUp className="w-5 h-5 text-warning" />
                                <span className="text-xs text-base-content/60">Progress</span>
                            </div>
                            <div className="space-y-1">
                                <p className="text-2xl font-bold">Level {currentUser?.level || 1}</p>
                                <progress 
                                    className="progress progress-warning w-full h-2" 
                                    value={currentUser?.experience || 0} 
                                    max={currentUser?.experienceRequired || 1}
                                />
                                <p className="text-xs text-base-content/60">
                                    {currentUser?.experience || 0} / {currentUser?.experienceRequired || 0} XP
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* Combat Stats */}
                    <div className="card bg-base-300 shadow-lg">
                        <div className="card-body p-4">
                            <div className="flex items-center justify-between mb-2">
                                <Swords className="w-5 h-5 text-error" />
                                <span className="text-xs text-base-content/60">Combat Power</span>
                            </div>
                            <div className="space-y-1">
                                <p className="text-2xl font-bold">
                                    {(currentUser?.strength || 0) + (currentUser?.dexterity || 0) + (currentUser?.intelligence || 0)}
                                </p>
                                <p className="text-xs text-base-content/60">Total Offensive Stats</p>
                            </div>
                        </div>
                    </div>

                    {/* Daily Activity */}
                    <div className="card bg-base-300 shadow-lg">
                        <div className="card-body p-4">
                            <div className="flex items-center justify-between mb-2">
                                <Target className="w-5 h-5 text-success" />
                                <span className="text-xs text-base-content/60">Today's Activity</span>
                            </div>
                            <div className="space-y-1">
                                <p className="text-2xl font-bold">{currentUser?.actionsToday || 0}</p>
                                <p className="text-xs text-base-content/60">Actions Taken</p>
                            </div>
                        </div>
                    </div>

                    {/* Notifications */}
                    <div className="card bg-base-300 shadow-lg">
                        <div className="card-body p-4">
                            <div className="flex items-center justify-between mb-2">
                                <Trophy className="w-5 h-5 text-primary" />
                                <span className="text-xs text-base-content/60">Updates</span>
                            </div>
                            <div className="space-y-2">
                                <div className="flex items-center text-sm">
                                    <Inbox className="w-4 h-4 mr-2 text-info" />
                                    <span>{unreadNotifications?.unread ?? 0} Events</span>
                                </div>
                                <div className="flex items-center text-sm">
                                    <Mail className="w-4 h-4 mr-2 text-secondary" />
                                    <span>{unreadMessages?.unread ?? 0} Messages</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="mt-6">
                <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 space-y-6">
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                        {mainFeatures.map((feature, index) => (
                            <Link
                                key={index}
                                to={feature.locked ? "#" : feature.link}
                                className={`card bg-base-100 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 group ${
                                    feature.locked ? "opacity-60 cursor-not-allowed" : ""
                                }`}
                                onClick={(e) => feature.locked && e.preventDefault()}
                            >
                                <figure className="relative h-32 overflow-hidden rounded-lg">
                                    <img
                                        src={feature.background}
                                        alt={feature.title}
                                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                                    />
                                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />

                                    {/* Feature Icon */}
                                    <div className="absolute top-6 flex items-center justify-center">
                                        <div className="">
                                            <img
                                                src={feature.icon}
                                                alt={feature.title}
                                                className={cn(
                                                    "size-12 transition-transform group-hover:scale-110",
                                                    feature.locked && "grayscale"
                                                )}
                                            />
                                        </div>
                                    </div>

                                    {/* Lock Badge */}
                                    {feature.locked && (
                                        <div className="absolute top-2 right-2 badge badge-error badge-sm">
                                            {/* <Lock className="w-3 h-3 mr-1" /> */}
                                            {feature.lockedLevel}
                                        </div>
                                    )}

                                    {/* Points Badge */}
                                    {feature.points && !feature.locked && (
                                        <div className="absolute top-2 left-2 badge badge-warning badge-sm">
                                            {feature.points}
                                        </div>
                                    )}

                                    <div className="absolute bottom-0 bg-white/10 backdrop-blur-sm py-1 w-full px-3">
                                        <h3 className="font-semibold text-sm group-hover:text-custom-yellow">
                                            {feature.title}
                                        </h3>
                                        <p className="text-xs text-base-content/60 hidden sm:block">
                                            {feature.subtitle}
                                        </p>
                                    </div>
                                </figure>

                                {/* <div className="card-body p-3">
                                    <h3 className="font-semibold text-sm">{feature.title}</h3>
                                    <p className="text-xs text-base-content/60 hidden sm:block">{feature.subtitle}</p>
                                </div> */}
                            </Link>
                        ))}
                    </div>

                    <div className="grid md:grid-cols-2 gap-6">
                        <Accomodation />
                        <Updates />
                    </div>
                </div>

                <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 mt-6">
                    <div className="card bg-base-300 shadow-xl">
                        <div className="card-body p-4">
                            <h2 className="card-title text-sm">
                                <GraduationCap className="w-5 h-5" />
                                Latest News
                            </h2>
                            <div className="space-y-2">
                                {latestNews.map((newspost) => (
                                    <Link
                                        key={newspost.id}
                                        to={newspost.href}
                                        className="block p-3 rounded-lg bg-base-200 hover:bg-base-100 transition-colors group"
                                    >
                                        <h3 className="font-semibold text-sm group-hover:text-primary transition-colors">
                                            {newspost.name}
                                        </h3>
                                        <p className="text-xs text-base-content/60">
                                            {newspost.username} • {newspost.date}
                                        </p>
                                    </Link>
                                ))}
                            </div>
                            <div className="card-actions justify-end mt-2">
                                <Link to="/news" className="btn btn-sm cursor-pointer">
                                    View All
                                </Link>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Home;
