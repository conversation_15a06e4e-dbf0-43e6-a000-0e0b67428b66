import React, { useState, useEffect } from "react";
import { NavLink, useLocation } from "react-router-dom";
import { navItems } from "@/helpers/navItems";
import { cn } from "@/lib/utils";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { format } from "date-fns";
import { UTCDateMini } from "@date-fns/utc";
import { usePersistStore } from "@/app/store/stores";
import {
    Home,
    User,
    Map,
    GraduationCap,
    ListTodo,
    Users,
    Settings,
    Heart,
    Shield,
    Zap,
    Brain,
    Activity,
    TrendingUp,
    DollarSign,
    Star,
    Package,
    Dumbbell,
    Sparkles,
    BookOpen,
    Calendar,
} from "lucide-react";

// Enhanced icon mapping with new icons for card-based navigation
const iconMap = {
    home: Home,
    character: User,
    explore: Map,
    campus: GraduationCap,
    tasks: ListTodo,
    social: Users,
    settings: Settings,
};

// Card-based navigation items with gradient themes
const cardNavItems = [
    {
        name: "Character",
        href: "/character",
        icon: User,
        description: "Manage your character",
        gradient: "from-purple-600 via-purple-500 to-purple-700",
        iconColor: "text-purple-100",
        bgPattern: "bg-[radial-gradient(circle_at_30%_20%,rgba(255,255,255,0.1),transparent_50%)]",
    },
    {
        name: "Inventory",
        href: "/inventory",
        icon: Package,
        description: "Manage your items",
        gradient: "from-blue-600 via-blue-500 to-blue-700",
        iconColor: "text-blue-100",
        bgPattern: "bg-[radial-gradient(circle_at_70%_30%,rgba(255,255,255,0.1),transparent_50%)]",
    },
    {
        name: "Training",
        href: "/campus",
        icon: Dumbbell,
        description: "Train your stats",
        gradient: "from-orange-600 via-orange-500 to-red-600",
        iconColor: "text-orange-100",
        bgPattern: "bg-[radial-gradient(circle_at_50%_80%,rgba(255,255,255,0.1),transparent_50%)]",
    },
    {
        name: "Talents",
        href: "/explore",
        icon: Sparkles,
        description: "Unlock new abilities",
        gradient: "from-green-600 via-green-500 to-emerald-600",
        iconColor: "text-green-100",
        bgPattern: "bg-[radial-gradient(circle_at_20%_70%,rgba(255,255,255,0.1),transparent_50%)]",
    },
    {
        name: "Abilities",
        href: "/social",
        icon: BookOpen,
        description: "Master your craft",
        gradient: "from-pink-600 via-purple-500 to-indigo-600",
        iconColor: "text-pink-100",
        bgPattern: "bg-[radial-gradient(circle_at_80%_20%,rgba(255,255,255,0.1),transparent_50%)]",
    },
    {
        name: "Daily Tasks",
        href: "/tasks",
        icon: Calendar,
        description: "Complete challenges",
        gradient: "from-cyan-600 via-blue-500 to-indigo-600",
        iconColor: "text-cyan-100",
        bgPattern: "bg-[radial-gradient(circle_at_40%_60%,rgba(255,255,255,0.1),transparent_50%)]",
    },
];

interface StatBarProps {
    label: string;
    value: number;
    max: number;
    color: string;
    icon: React.ReactNode;
}

const StatBar: React.FC<StatBarProps> = ({ label, value, max, color, icon }) => {
    const percentage = (value / max) * 100;

    return (
        <div className="mb-3">
            <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 p-2 rounded border border-slate-600/50">
                {/* Header */}
                <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center gap-2">
                        <div className="p-1 bg-gradient-to-br from-slate-700 to-slate-800 rounded border border-blue-400/40">
                            {React.cloneElement(icon as React.ReactElement, {
                                className: "w-3 h-3 text-blue-300",
                            })}
                        </div>
                        <span className="text-[11px] font-semibold uppercase tracking-wider text-blue-200">
                            {label}
                        </span>
                    </div>
                    <div className="px-2">
                        <span className="text-xs font-mono font-semibold text-slate-300">
                            {value}/{max}
                        </span>
                    </div>
                </div>

                {/* Progress bar container */}
                <div className="relative h-3 bg-black/60 rounded overflow-hidden border border-slate-700/40">
                    {/* Progress fill */}
                    <div
                        className={cn("h-full transition-all duration-700 ease-out relative", color)}
                        style={{ width: `${percentage}%` }}
                    >
                        {/* Subtle highlight */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-white/5" />
                    </div>
                </div>
            </div>
        </div>
    );
};

export default function GameSidebar() {
    const location = useLocation();
    const { data: currentUser } = useFetchCurrentUser();
    const { twelveHrClock } = usePersistStore();
    const [currentTime, setCurrentTime] = useState(new Date());

    const navigation = navItems(currentUser?.userType ?? null);

    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentTime(new Date());
        }, 1000);

        return () => clearInterval(timer);
    }, []);

    const timeFormat = twelveHrClock ? "h:mm:ss a" : "HH:mm:ss";

    return (
        <div className="fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 border-r-2 border-slate-700/50 rounded-r-xl">
            {/* Modern background texture */}
            <div className="absolute inset-0 bg-[url('/assets/gta-theme-bg.png')] bg-cover bg-center pointer-events-none opacity-10 rounded-r-xl" />
            <div className="absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-slate-900/20 pointer-events-none rounded-r-xl" />

            <div className="flex flex-col h-full relative z-10">
                {/* Header with logo/title */}
                <div className="p-6 border-b border-slate-600/40 bg-gradient-to-b from-slate-900/95 via-slate-800/90 to-slate-900/95 relative">
                    {/* Modern corner accents */}
                    <div className="absolute top-2 left-2 w-6 h-6 border-t-2 border-l-2 border-blue-400/60" />
                    <div className="absolute top-2 right-2 w-6 h-6 border-t-2 border-r-2 border-blue-400/60" />
                    <div className="absolute bottom-2 left-2 w-6 h-6 border-b-2 border-l-2 border-blue-400/60" />
                    <div className="absolute bottom-2 right-2 w-6 h-6 border-b-2 border-r-2 border-blue-400/60" />

                    {/* Subtle accent lines */}
                    <div className="absolute inset-0 opacity-40">
                        <div className="absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-blue-400 to-transparent" />
                        <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-blue-400 to-transparent" />
                    </div>

                    <div className="text-center relative z-10">
                        <h1 className="text-4xl font-bold text-white tracking-wide">CHIKARA</h1>
                        <div className="flex items-center justify-center gap-2">
                            <div className="h-0.5 flex-1 bg-gradient-to-r from-transparent to-blue-400/60" />
                            <p className="text-xs font-bold text-blue-300 uppercase tracking-wide px-2">ACADEMY</p>
                            <div className="h-0.5 flex-1 bg-gradient-to-l from-transparent to-blue-400/60" />
                        </div>
                    </div>
                </div>

                {/* User Profile Section */}
                {currentUser && (
                    <div className="p-5 border-b border-slate-600/30 bg-gradient-to-b from-slate-900/90 via-slate-800/85 to-slate-900/90 relative">
                        {/* Player info header */}
                        <div className="flex items-center gap-4 mb-4">
                            <div className="relative group">
                                <div className="relative bg-gradient-to-br from-slate-800 via-slate-700 to-slate-800 rounded p-1 border border-blue-400/50">
                                    <img
                                        src={currentUser.avatar || "/default-avatar.png"}
                                        alt={currentUser.username}
                                        className="w-16 h-16 rounded object-cover"
                                    />
                                    {/* Status indicator */}
                                    <div className="absolute -bottom-1 -right-1">
                                        <div className="bg-green-500 w-4 h-4 rounded-full border-2 border-slate-800" />
                                    </div>
                                </div>
                            </div>

                            <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                    <h3 className="font-bold text-white text-xl uppercase">{currentUser.username}</h3>
                                </div>
                                <div className="flex items-center gap-2">
                                    <div className="bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 px-3 py-1 rounded border border-blue-400/60">
                                        <p className="text-xs font-bold text-white uppercase">
                                            LVL {currentUser.level}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Stats */}
                        <div className="space-y-3">
                            <StatBar
                                label="HEALTH"
                                value={currentUser.currentHealth || 0}
                                max={currentUser.health || 100}
                                color="bg-gradient-to-r from-red-600 via-red-500 to-red-600"
                                icon={<Heart className="w-4 h-4 text-red-400" />}
                            />
                            <StatBar
                                label="STAMINA"
                                value={currentUser.actionPoints || 0}
                                max={currentUser.maxActionPoints || 100}
                                color="bg-gradient-to-r from-yellow-500 via-yellow-400 to-orange-600"
                                icon={<Zap className="w-4 h-4 text-yellow-400" />}
                            />
                            <StatBar
                                label="EXPERIENCE"
                                value={currentUser.xp || 0}
                                max={currentUser.xpForNextLevel || 100}
                                color="bg-gradient-to-r from-blue-600 via-blue-500 to-purple-600"
                                icon={<TrendingUp className="w-4 h-4 text-blue-400" />}
                            />
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 gap-3 mt-4">
                            <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 border border-green-500/60 p-3 relative overflow-hidden group hover:border-green-400/80 transition-all duration-300">
                                {/* Corner accents */}
                                <div className="absolute top-1 left-1 w-3 h-3 border-t border-l border-green-500/70" />
                                <div className="absolute top-1 right-1 w-3 h-3 border-t border-r border-green-500/70" />
                                <div className="absolute bottom-1 left-1 w-3 h-3 border-b border-l border-green-500/70" />
                                <div className="absolute bottom-1 right-1 w-3 h-3 border-b border-r border-green-500/70" />

                                {/* Hover effect */}
                                <div className="absolute inset-0 bg-green-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                                <div className="relative z-10">
                                    <div className="flex items-center gap-2 mb-2">
                                        <div className="p-1 bg-gradient-to-r from-green-600 to-green-500 rounded border border-green-500/60">
                                            <DollarSign className="w-3 h-3 text-white" />
                                        </div>
                                        <span className="text-[9px] font-black text-green-300 uppercase tracking-[0.2em]">
                                            YEN
                                        </span>
                                    </div>
                                    <p className="text-lg font-mono font-black text-white">
                                        ¥{(currentUser.cash || 0).toLocaleString()}
                                    </p>
                                </div>
                            </div>

                            <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/80 to-slate-800/90 border border-purple-500/60 p-3 relative overflow-hidden group hover:border-purple-400/80 transition-all duration-300">
                                {/* Corner accents */}
                                <div className="absolute top-1 left-1 w-3 h-3 border-t border-l border-purple-500/70" />
                                <div className="absolute top-1 right-1 w-3 h-3 border-t border-r border-purple-500/70" />
                                <div className="absolute bottom-1 left-1 w-3 h-3 border-b border-l border-purple-500/70" />
                                <div className="absolute bottom-1 right-1 w-3 h-3 border-b border-r border-purple-500/70" />

                                {/* Hover effect */}
                                <div className="absolute inset-0 bg-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                                <div className="relative z-10">
                                    <div className="flex items-center gap-2 mb-2">
                                        <div className="p-1 bg-gradient-to-r from-purple-600 to-purple-500 rounded border border-purple-500/60">
                                            <Star className="w-3 h-3 text-white" />
                                        </div>
                                        <span className="text-[9px] font-black text-purple-300 uppercase tracking-[0.2em]">
                                            REP
                                        </span>
                                    </div>
                                    <p className="text-lg font-mono font-black text-white">
                                        {currentUser.reputation || 0}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Card-based Navigation */}
                <nav className="flex-1 p-4 overflow-y-auto scrollbar-thin scrollbar-thumb-orange-600 scrollbar-track-black/50">
                    <div className="grid grid-cols-2 gap-3">
                        {cardNavItems.map((item) => {
                            const Icon = item.icon;
                            const isActive = location.pathname === item.href;

                            return (
                                <NavLink
                                    key={item.name}
                                    to={item.href}
                                    className={cn(
                                        "group relative overflow-hidden rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg",
                                        isActive
                                            ? "border-white/30 shadow-lg scale-105"
                                            : "border-white/10 hover:border-white/20"
                                    )}
                                >
                                    {/* Main gradient background */}
                                    <div
                                        className={cn(
                                            "absolute inset-0 bg-gradient-to-br opacity-90 group-hover:opacity-100 transition-opacity duration-300",
                                            item.gradient
                                        )}
                                    />

                                    {/* Background pattern overlay */}
                                    <div
                                        className={cn(
                                            "absolute inset-0 opacity-30 group-hover:opacity-50 transition-opacity duration-300",
                                            item.bgPattern
                                        )}
                                    />

                                    {/* Subtle texture overlay */}
                                    <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-black/10 opacity-40" />

                                    {/* Content */}
                                    <div className="relative z-10 p-4 h-24 flex flex-col justify-between">
                                        {/* Icon */}
                                        <div className="flex justify-center mb-2">
                                            <div
                                                className={cn(
                                                    "p-2 rounded-lg bg-white/20 backdrop-blur-sm border border-white/30 group-hover:bg-white/30 transition-all duration-300",
                                                    isActive && "bg-white/30 shadow-lg"
                                                )}
                                            >
                                                <Icon
                                                    className={cn(
                                                        "w-6 h-6 transition-all duration-300",
                                                        item.iconColor,
                                                        isActive && "drop-shadow-sm"
                                                    )}
                                                />
                                            </div>
                                        </div>

                                        {/* Text content */}
                                        <div className="text-center">
                                            <h3 className="text-white font-bold text-sm mb-1 drop-shadow-sm">
                                                {item.name}
                                            </h3>
                                            <p className="text-white/80 text-xs leading-tight drop-shadow-sm">
                                                {item.description}
                                            </p>
                                        </div>
                                    </div>

                                    {/* Active indicator */}
                                    {isActive && (
                                        <div className="absolute inset-0 border-2 border-white/50 rounded-xl animate-pulse" />
                                    )}

                                    {/* Hover glow effect */}
                                    <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />
                                </NavLink>
                            );
                        })}
                    </div>

                    {/* Additional navigation items (Settings, etc.) */}
                    <div className="mt-6 space-y-2">
                        {navigation
                            .filter((item) => !cardNavItems.some((card) => card.href === item.href))
                            .map((item) => {
                                const Icon = iconMap[item.current as keyof typeof iconMap] || Settings;
                                const isActive = location.pathname === item.href;

                                return (
                                    <NavLink
                                        key={item.name}
                                        to={item.href}
                                        className={cn(
                                            "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-300 group relative overflow-hidden border text-sm",
                                            isActive
                                                ? "bg-gradient-to-r from-blue-600/60 via-blue-500/50 to-blue-600/60 text-white border-blue-400/80"
                                                : "text-slate-400 hover:text-white bg-black/40 hover:bg-gradient-to-r hover:from-blue-900/30 hover:to-black border-slate-700/60 hover:border-blue-500/60"
                                        )}
                                    >
                                        <div
                                            className={cn(
                                                "p-1.5 rounded border transition-all duration-300",
                                                isActive
                                                    ? "bg-gradient-to-r from-blue-600 to-blue-500 border-blue-400/80"
                                                    : "bg-black/60 border-slate-700/60 group-hover:border-blue-500/60"
                                            )}
                                        >
                                            <Icon
                                                className={cn(
                                                    "w-4 h-4 transition-all duration-300",
                                                    isActive ? "text-white" : "text-slate-500 group-hover:text-blue-400"
                                                )}
                                            />
                                        </div>
                                        <span
                                            className={cn(
                                                "font-medium transition-all duration-300",
                                                isActive ? "text-white" : "text-slate-400 group-hover:text-white"
                                            )}
                                        >
                                            {item.name}
                                        </span>
                                    </NavLink>
                                );
                            })}
                    </div>
                </nav>

                {/* Footer */}
                <div className="p-4 border-t border-slate-600/50 bg-gradient-to-b from-slate-900/60 via-black to-slate-900 relative">
                    {/* Corner elements */}
                    <div className="absolute top-0 left-4 w-6 h-6">
                        <div className="absolute top-0 left-0 w-3 h-0.5 bg-blue-400/80" />
                        <div className="absolute top-0 left-0 w-0.5 h-3 bg-blue-400/80" />
                    </div>
                    <div className="absolute top-0 right-4 w-6 h-6">
                        <div className="absolute top-0 right-0 w-3 h-0.5 bg-blue-400/80" />
                        <div className="absolute top-0 right-0 w-0.5 h-3 bg-blue-400/80" />
                    </div>

                    {/* System Clock */}
                    <div className="bg-gradient-to-br from-slate-800/90 via-slate-700/70 to-black/90 border border-blue-400/60 p-4 mb-3 relative overflow-hidden group hover:border-blue-300/80 transition-all duration-300">
                        {/* Background pattern */}
                        <div className="absolute inset-0 bg-[repeating-linear-gradient(45deg,transparent,transparent_8px,rgba(59,130,246,0.02)_8px,rgba(59,130,246,0.02)_16px)]" />

                        {/* Hover effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-blue-500/8 to-blue-600/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                        <div className="relative z-10">
                            <div className="flex items-center gap-2 mb-2">
                                <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" />
                                <p className="text-[10px] font-black text-blue-300 uppercase tracking-[0.3em]">
                                    SYSTEM TIME
                                </p>
                            </div>
                            <p className="text-2xl font-mono font-black text-white tracking-wider">
                                {format(new UTCDateMini(), timeFormat)}
                            </p>
                        </div>
                    </div>

                    {/* Version info */}
                    <div className="text-center relative">
                        <div className="flex items-center justify-center gap-2 mb-1">
                            <div className="h-px flex-1 bg-gradient-to-r from-transparent to-blue-400/50" />
                            <div className="w-1.5 h-1.5 bg-blue-400/80 rounded-full" />
                            <div className="h-px flex-1 bg-gradient-to-l from-transparent to-blue-400/50" />
                        </div>
                        <p className="text-[9px] font-black text-slate-500 uppercase tracking-[0.2em]">
                            BUILD {import.meta.env.VITE_PACKAGE_VERSION || "1.0.0"}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
}
