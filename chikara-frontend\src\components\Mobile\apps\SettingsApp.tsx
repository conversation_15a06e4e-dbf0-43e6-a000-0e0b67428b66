import React from 'react';
import { motion } from 'framer-motion';
import { 
  User, 
  Bell, 
  Shield, 
  Volume2, 
  Globe, 
  Moon, 
  HelpCircle, 
  LogOut,
  ChevronRight,
  Wifi,
  Battery
} from 'lucide-react';

const SettingsApp: React.FC = () => {
  const settingsGroups = [
    {
      title: 'Account',
      items: [
        { icon: User, label: 'Profile', value: 'Shadow Warrior' },
        { icon: Shield, label: 'Privacy & Security', value: '' },
        { icon: Bell, label: 'Notifications', value: 'On' },
      ],
    },
    {
      title: 'Preferences',
      items: [
        { icon: Volume2, label: 'Sound & Vibration', value: 'On' },
        { icon: Globe, label: 'Language', value: 'English' },
        { icon: Moon, label: 'Display', value: 'Dark' },
      ],
    },
    {
      title: 'System',
      items: [
        { icon: Wifi, label: 'Network', value: 'Connected' },
        { icon: Battery, label: 'Battery', value: '87%' },
        { icon: HelpCircle, label: 'Help & Support', value: '' },
      ],
    },
  ];

  return (
    <div className="h-full bg-gray-900 overflow-y-auto">
      {/* User Info */}
      <div className="bg-gradient-to-r from-gray-800 to-gray-700 p-6">
        <div className="flex items-center gap-4">
          <div className="w-20 h-20 bg-purple-600 rounded-full flex items-center justify-center">
            <span className="text-2xl font-bold text-white">SW</span>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Shadow Warrior</h3>
            <p className="text-gray-300">Level 32 • Gang Member</p>
            <p className="text-gray-400 text-sm">ID: #12345</p>
          </div>
        </div>
      </div>

      {/* Settings Groups */}
      <div className="p-4 space-y-6">
        {settingsGroups.map((group, groupIndex) => (
          <div key={group.title}>
            <h4 className="text-gray-400 text-sm font-medium mb-2 px-2">{group.title}</h4>
            <div className="bg-gray-800 rounded-lg overflow-hidden">
              {group.items.map((item, index) => (
                <motion.button
                  key={item.label}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: (groupIndex * 3 + index) * 0.05 }}
                  className="w-full p-4 flex items-center justify-between hover:bg-gray-700 transition-colors border-b border-gray-700 last:border-0"
                >
                  <div className="flex items-center gap-3">
                    <item.icon className="w-5 h-5 text-gray-400" />
                    <span className="text-white">{item.label}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {item.value && (
                      <span className="text-gray-400 text-sm">{item.value}</span>
                    )}
                    <ChevronRight className="w-4 h-4 text-gray-500" />
                  </div>
                </motion.button>
              ))}
            </div>
          </div>
        ))}

        {/* Logout Button */}
        <motion.button
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="w-full bg-red-500/20 hover:bg-red-500/30 text-red-400 py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
        >
          <LogOut className="w-5 h-5" />
          Log Out
        </motion.button>

        {/* App Version */}
        <div className="text-center py-4">
          <p className="text-gray-500 text-sm">Chikara Mobile</p>
          <p className="text-gray-600 text-xs">Version 1.0.0</p>
        </div>
      </div>
    </div>
  );
};

export default SettingsApp;
