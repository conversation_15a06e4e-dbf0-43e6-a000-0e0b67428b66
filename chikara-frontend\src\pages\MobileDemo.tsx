import React, { useState } from 'react';
import { Smartphone } from 'lucide-react';
import { Mobile } from '@/components/Mobile';

const MobileDemo: React.FC = () => {
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-8">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-white mb-4">Chikara Academy Mobile</h1>
        <p className="text-gray-400 mb-8">Experience the GTA-style mobile interface</p>
        
        <button
          onClick={() => setIsMobileOpen(true)}
          className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto"
        >
          <Smartphone className="w-5 h-5" />
          Open Mobile Phone
        </button>

        <div className="mt-12 text-left max-w-md mx-auto">
          <h2 className="text-xl font-semibold text-white mb-4">Features:</h2>
          <ul className="space-y-2 text-gray-300">
            <li>• Messages - Chat with gang members and NPCs</li>
            <li>• Phone - Make calls and manage contacts</li>
            <li>• Gang - View gang information and activities</li>
            <li>• Map - Navigate the city and find locations</li>
            <li>• Bank - Manage your finances and currencies</li>
            <li>• Settings - Customize your mobile experience</li>
            <li>• And many more apps coming soon!</li>
          </ul>
        </div>
      </div>

      <Mobile isOpen={isMobileOpen} onClose={() => setIsMobileOpen(false)} />
    </div>
  );
};

export default MobileDemo;
