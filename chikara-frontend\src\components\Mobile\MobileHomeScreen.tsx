import React from "react";
import { motion } from "framer-motion";
import {
    Phone,
    MessageSquare,
    Settings,
    Map,
    Users,
    Calendar,
    Trophy,
    ShoppingBag,
    Newspaper,
    Banknote,
    Home,
    GraduationCap,
} from "lucide-react";
import { MobileAppType } from "./types";

interface MobileHomeScreenProps {
    onAppOpen: (appType: MobileAppType) => void;
}

const apps = [
    { id: "messages" as MobileAppType, name: "Messages", icon: MessageSquare, color: "bg-green-500" },
    { id: "phone" as MobileAppType, name: "Phone", icon: Phone, color: "bg-green-600" },
    { id: "gang" as MobileAppType, name: "<PERSON>", icon: Users, color: "bg-purple-500" },
    { id: "map" as MobileAppType, name: "Map", icon: Map, color: "bg-blue-500" },
    { id: "calendar" as MobileAppType, name: "Events", icon: Calendar, color: "bg-red-500" },
    { id: "bank" as MobileAppType, name: "Bank", icon: Banknote, color: "bg-yellow-500" },
    { id: "shop" as MobileAppType, name: "Shop", icon: ShoppingBag, color: "bg-orange-500" },
    { id: "news" as MobileAppType, name: "News", icon: Newspaper, color: "bg-gray-700" },
    { id: "rankings" as MobileAppType, name: "Rankings", icon: Trophy, color: "bg-amber-500" },
    { id: "property" as MobileAppType, name: "Property", icon: Home, color: "bg-indigo-500" },
    { id: "academy" as MobileAppType, name: "Academy", icon: GraduationCap, color: "bg-blue-600" },
    { id: "settings" as MobileAppType, name: "Settings", icon: Settings, color: "bg-gray-500" },
];

const MobileHomeScreen: React.FC<MobileHomeScreenProps> = ({ onAppOpen }) => {
    return (
        <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="h-full bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6"
        >
            {/* Wallpaper Effect */}
            <div className="absolute inset-0 opacity-20">
                <div className="absolute inset-0 bg-[url('/mobile-wallpaper.jpg')] bg-cover bg-center" />
            </div>

            {/* App Grid */}
            <div className="relative grid grid-cols-4 gap-5 mt-8">
                {apps.map((app, index) => (
                    <motion.button
                        key={app.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        onClick={() => onAppOpen(app.id)}
                        className="flex flex-col items-center gap-2 group"
                    >
                        <motion.div
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            className={`w-16 h-16 ${app.color} rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow`}
                        >
                            <app.icon className="w-8 h-8 text-white" />
                        </motion.div>
                        <span className="text-xs text-white/80 font-medium">{app.name}</span>
                    </motion.button>
                ))}
            </div>
        </motion.div>
    );
};

export default MobileHomeScreen;
