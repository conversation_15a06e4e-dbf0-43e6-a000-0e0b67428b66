import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { MapPin, Navigation, Zap, Users, Store, Swords } from 'lucide-react';

const districts = [
  { 
    id: '1', 
    name: 'Shibuya District', 
    controlled: 'Shadow Dragons',
    danger: 'High',
    locations: 12,
    color: 'purple'
  },
  { 
    id: '2', 
    name: 'Harajuku Zone', 
    controlled: 'Red Tigers',
    danger: 'Medium',
    locations: 8,
    color: 'red'
  },
  { 
    id: '3', 
    name: 'Akihabara Tech', 
    controlled: 'Neutral',
    danger: 'Low',
    locations: 15,
    color: 'gray'
  },
  { 
    id: '4', 
    name: 'Shinjuku Heights', 
    controlled: 'Blue Serpents',
    danger: 'High',
    locations: 10,
    color: 'blue'
  },
];

const locations = [
  { id: '1', name: 'Main Dojo', type: 'training', icon: Swords, distance: '0.2 km' },
  { id: '2', name: 'Gang Hideout', type: 'gang', icon: Users, distance: '0.5 km' },
  { id: '3', name: 'Black Market', type: 'shop', icon: Store, distance: '1.2 km' },
  { id: '4', name: 'Power Shrine', type: 'special', icon: Zap, distance: '2.1 km' },
];

const MapApp: React.FC = () => {
  const [selectedDistrict, setSelectedDistrict] = useState(districts[0]);

  const getDangerColor = (danger: string) => {
    switch (danger) {
      case 'High': return 'text-red-400';
      case 'Medium': return 'text-yellow-400';
      case 'Low': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const getDistrictColor = (color: string) => {
    switch (color) {
      case 'purple': return 'bg-purple-600';
      case 'red': return 'bg-red-600';
      case 'blue': return 'bg-blue-600';
      default: return 'bg-gray-600';
    }
  };

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Map View */}
      <div className="relative h-64 bg-gray-800 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 to-gray-700">
          {/* Simple map representation */}
          <div className="relative h-full">
            {districts.map((district, index) => (
              <motion.div
                key={district.id}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => setSelectedDistrict(district)}
                className={`absolute w-16 h-16 ${getDistrictColor(district.color)} rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform`}
                style={{
                  top: `${20 + (index % 2) * 40}%`,
                  left: `${15 + (index * 20)}%`,
                }}
              >
                <MapPin className="w-6 h-6 text-white" />
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Current Location Indicator */}
        <div className="absolute top-4 left-4 bg-gray-900/80 backdrop-blur-sm rounded-lg p-2 flex items-center gap-2">
          <Navigation className="w-4 h-4 text-blue-400" />
          <span className="text-white text-sm">Your Location</span>
        </div>
      </div>

      {/* District Info */}
      <div className="bg-gray-800 p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-white font-semibold text-lg">{selectedDistrict.name}</h3>
            <p className="text-gray-400 text-sm">Controlled by {selectedDistrict.controlled}</p>
          </div>
          <div className="text-right">
            <p className={`font-medium ${getDangerColor(selectedDistrict.danger)}`}>
              {selectedDistrict.danger} Danger
            </p>
            <p className="text-gray-400 text-xs">{selectedDistrict.locations} locations</p>
          </div>
        </div>
      </div>

      {/* Locations List */}
      <div className="flex-1 overflow-y-auto p-4">
        <h4 className="text-white font-medium mb-3">Nearby Locations</h4>
        <div className="space-y-2">
          {locations.map((location, index) => (
            <motion.div
              key={location.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05 }}
              className="bg-gray-800 rounded-lg p-4 flex items-center gap-3 hover:bg-gray-700 transition-colors cursor-pointer"
            >
              <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center">
                <location.icon className="w-6 h-6 text-purple-400" />
              </div>
              <div className="flex-1">
                <h5 className="text-white font-medium">{location.name}</h5>
                <p className="text-gray-400 text-sm capitalize">{location.type}</p>
              </div>
              <div className="text-right">
                <p className="text-gray-300 text-sm">{location.distance}</p>
                <p className="text-gray-500 text-xs">away</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Quick Travel */}
      <div className="p-4 bg-gray-800 border-t border-gray-700">
        <button className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
          <Navigation className="w-5 h-5" />
          Quick Travel to {selectedDistrict.name}
        </button>
      </div>
    </div>
  );
};

export default MapApp;
