import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Send, Search } from 'lucide-react';
import { MobileMessage } from '../types';

const mockMessages: MobileMessage[] = [
  {
    id: '1',
    sender: 'Gang Leader',
    avatar: '/avatars/leader.jpg',
    message: 'Meeting at the dojo in 30 minutes. Don\'t be late!',
    time: '10:45 AM',
    unread: true,
  },
  {
    id: '2',
    sender: '<PERSON><PERSON>da',
    avatar: '/avatars/sensei.jpg',
    message: 'Your training session is scheduled for tomorrow at 6 AM.',
    time: '9:30 AM',
    unread: true,
  },
  {
    id: '3',
    sender: 'Rival Gang',
    avatar: '/avatars/rival.jpg',
    message: 'You think you\'re tough? Meet us at the old warehouse.',
    time: 'Yesterday',
    unread: false,
  },
  {
    id: '4',
    sender: 'Shop Owner',
    avatar: '/avatars/shop.jpg',
    message: 'New equipment has arrived! Come check it out.',
    time: '2 days ago',
    unread: false,
  },
];

const MessagesApp: React.FC = () => {
  const [selectedMessage, setSelectedMessage] = useState<MobileMessage | null>(null);
  const [messageInput, setMessageInput] = useState('');

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      // Handle sending message
      setMessageInput('');
    }
  };

  if (selectedMessage) {
    return (
      <div className="h-full flex flex-col bg-gray-900">
        {/* Conversation Header */}
        <div className="bg-gray-800 p-4 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gray-600 rounded-full" />
            <div>
              <h3 className="text-white font-medium">{selectedMessage.sender}</h3>
              <p className="text-gray-400 text-sm">Active now</p>
            </div>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          <div className="flex gap-3">
            <div className="w-8 h-8 bg-gray-600 rounded-full flex-shrink-0" />
            <div className="bg-gray-800 rounded-2xl rounded-tl-sm p-3 max-w-[80%]">
              <p className="text-white text-sm">{selectedMessage.message}</p>
              <span className="text-gray-500 text-xs mt-1">{selectedMessage.time}</span>
            </div>
          </div>
        </div>

        {/* Input */}
        <div className="p-4 bg-gray-800 border-t border-gray-700">
          <div className="flex gap-2">
            <input
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Type a message..."
              className="flex-1 bg-gray-700 text-white px-4 py-2 rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={handleSendMessage}
              className="p-2 bg-blue-500 hover:bg-blue-600 rounded-full transition-colors"
            >
              <Send className="w-5 h-5 text-white" />
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-900">
      {/* Search Bar */}
      <div className="p-4 bg-gray-800 border-b border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search messages..."
            className="w-full bg-gray-700 text-white pl-10 pr-4 py-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Message List */}
      <div className="overflow-y-auto">
        {mockMessages.map((message, index) => (
          <motion.button
            key={message.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
            onClick={() => setSelectedMessage(message)}
            className="w-full p-4 flex items-start gap-3 hover:bg-gray-800 transition-colors border-b border-gray-800"
          >
            <div className="w-12 h-12 bg-gray-600 rounded-full flex-shrink-0" />
            <div className="flex-1 text-left">
              <div className="flex justify-between items-start mb-1">
                <h3 className="text-white font-medium">{message.sender}</h3>
                <span className="text-gray-400 text-xs">{message.time}</span>
              </div>
              <p className="text-gray-400 text-sm line-clamp-1">{message.message}</p>
            </div>
            {message.unread && (
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
            )}
          </motion.button>
        ))}
      </div>
    </div>
  );
};

export default MessagesApp;
