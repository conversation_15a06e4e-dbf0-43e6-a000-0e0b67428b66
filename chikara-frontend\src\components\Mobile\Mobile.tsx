import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ArrowLeft, Phone, MessageSquare, Camera, Settings, Map, Users, Calendar, Music } from 'lucide-react';
import { cn } from '@/lib/utils';
import MobileApp from './MobileApp';
import MobileHeader from './MobileHeader';
import MobileHomeScreen from './MobileHomeScreen';
import { MobileAppType } from './types';

interface MobileProps {
  isOpen: boolean;
  onClose: () => void;
}

const Mobile: React.FC<MobileProps> = ({ isOpen, onClose }) => {
  const [currentApp, setCurrentApp] = useState<MobileAppType | null>(null);
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => setTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  const handleAppOpen = (appType: MobileAppType) => {
    setCurrentApp(appType);
  };

  const handleAppClose = () => {
    setCurrentApp(null);
  };

  const handleHomeButton = () => {
    setCurrentApp(null);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />

          {/* Phone Container */}
          <motion.div
            initial={{ y: '100%', opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: '100%', opacity: 0 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed bottom-4 right-4 z-50 flex flex-col items-center"
          >
            {/* Phone Frame */}
            <div className="relative bg-gray-900 rounded-[3rem] p-2 shadow-2xl">
              {/* Phone Notch */}
              <div className="absolute top-6 left-1/2 transform -translate-x-1/2 w-32 h-6 bg-gray-900 rounded-b-2xl z-20" />
              
              {/* Phone Screen */}
              <div className="relative w-[380px] h-[820px] bg-black rounded-[2.5rem] overflow-hidden">
                {/* Status Bar */}
                <MobileHeader time={time} />

                {/* Screen Content */}
                <div className="relative h-full pt-12 pb-24">
                  <AnimatePresence mode="wait">
                    {currentApp ? (
                      <MobileApp
                        key={currentApp}
                        appType={currentApp}
                        onClose={handleAppClose}
                      />
                    ) : (
                      <MobileHomeScreen
                        key="home"
                        onAppOpen={handleAppOpen}
                      />
                    )}
                  </AnimatePresence>
                </div>

                {/* Home Indicator / Navigation */}
                <div className="absolute bottom-0 left-0 right-0 h-24 bg-gray-950/90 backdrop-blur-md border-t border-gray-800">
                  <div className="flex items-center justify-center h-full gap-6">
                    {currentApp && (
                      <button
                        onClick={handleAppClose}
                        className="p-3 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
                      >
                        <ArrowLeft className="w-6 h-6 text-white" />
                      </button>
                    )}
                    <button
                      onClick={handleHomeButton}
                      className="p-3 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
                    >
                      <div className="w-6 h-6 rounded bg-white" />
                    </button>
                    <button
                      onClick={onClose}
                      className="p-3 rounded-full bg-gray-800 hover:bg-gray-700 transition-colors"
                    >
                      <X className="w-6 h-6 text-white" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Phone Side Buttons */}
              <div className="absolute -right-2 top-32 w-1 h-16 bg-gray-800 rounded-r" />
              <div className="absolute -right-2 top-52 w-1 h-12 bg-gray-800 rounded-r" />
              <div className="absolute -right-2 top-68 w-1 h-12 bg-gray-800 rounded-r" />
              <div className="absolute -left-2 top-44 w-1 h-20 bg-gray-800 rounded-l" />
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default Mobile;
