import React from "react";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import { MobileAppType } from "./types";
import MessagesApp from "./apps/MessagesApp";
import PhoneApp from "./apps/PhoneApp";
import GangApp from "./apps/GangApp";
import MapApp from "./apps/MapApp";
import BankApp from "./apps/BankApp";
import SettingsApp from "./apps/SettingsApp";
import EventsApp from "./apps/EventsApp";
import PropertyApp from "./apps/PropertyApp";
import GenericApp from "./apps/GenericApp";

interface MobileAppProps {
    appType: MobileAppType;
    onClose: () => void;
}

const MobileApp: React.FC<MobileAppProps> = ({ appType, onClose }) => {
    const renderApp = () => {
        switch (appType) {
            case "messages":
                return <MessagesApp />;
            case "phone":
                return <PhoneApp />;
            case "gang":
                return <GangApp />;
            case "map":
                return <MapApp />;
            case "bank":
                return <BankApp />;
            case "calendar":
                return <EventsApp />;
            case "property":
                return <PropertyApp />;
            case "settings":
                return <SettingsApp />;
            default:
                return <GenericApp appType={appType} />;
        }
    };

    const getAppTitle = () => {
        switch (appType) {
            case "messages":
                return "Messages";
            case "phone":
                return "Phone";
            case "gang":
                return "Gang";
            case "map":
                return "Map";
            case "calendar":
                return "Events";
            case "bank":
                return "Bank";
            case "shop":
                return "Shop";
            case "news":
                return "News";
            case "arcade":
                return "Arcade";
            case "rankings":
                return "Rankings";
            case "property":
                return "Property";
            case "academy":
                return "Academy";
            case "settings":
                return "Settings";
            default:
                return "App";
        }
    };

    return (
        <motion.div
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="absolute inset-0 bg-gray-900 flex flex-col"
        >
            {/* App Header */}
            <div className="bg-gray-800 p-4 flex items-center gap-4 border-b border-gray-700">
                <button onClick={onClose} className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                    <ArrowLeft className="w-5 h-5 text-white" />
                </button>
                <h2 className="text-white text-lg font-semibold">{getAppTitle()}</h2>
            </div>

            {/* App Content */}
            <div className="flex-1 overflow-hidden">{renderApp()}</div>
        </motion.div>
    );
};

export default MobileApp;
