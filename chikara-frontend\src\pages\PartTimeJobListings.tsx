import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { ArrowLeft, Briefcase } from "lucide-react";
import { Link } from "react-router-dom";
import JobSelectCard from "../features/job/components/JobSelectCard";
import useJobList from "../features/job/api/useJobList";

export default function PartTimeJobListings() {
    const { data: currentUser } = useFetchCurrentUser();
    const { data: jobsList, isLoading } = useJobList();

    if (isLoading)
        return (
            <div className="flex h-screen items-center justify-center">
                <div className="text-lg text-gray-400">Loading job listings...</div>
            </div>
        );

    return (
        <div className="min-h-screen p-4 md:p-8">
            <div className="mx-auto max-w-7xl">
                {/* Header */}
                <div className="mb-8">
                    <div className="flex items-center justify-between mb-6">
                        {currentUser?.jobId !== null && (
                            <Link to={-1 as any}>
                                <button
                                    type="button"
                                    className="inline-flex items-center gap-2 rounded-lg bg-slate-700 px-4 py-2.5 font-medium text-white transition-colors hover:bg-slate-600"
                                >
                                    <ArrowLeft className="size-4" />
                                    <span className="text-sm">Back</span>
                                </button>
                            </Link>
                        )}
                    </div>
                </div>

                {/* Job Grid */}
                <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
                    {jobsList?.map((job) => (
                        <JobSelectCard
                            key={job.id}
                            job={job}
                            currentJob={currentUser?.jobId || 0}
                            currentUser={currentUser}
                        />
                    ))}
                </div>
            </div>
        </div>
    );
}
