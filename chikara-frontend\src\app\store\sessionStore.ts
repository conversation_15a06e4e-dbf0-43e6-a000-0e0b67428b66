import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

interface Room {
    id: number;
    name: string;
}

interface ChatRoom {
    displayName: string;
    room: Room;
}

interface SessionState {
    hideChat: boolean;
    setHideChat: (hideChat: boolean) => void;
    mainChatRoom: ChatRoom;
    setMainChatRoom: (mainChatRoom: ChatRoom) => void;
    inEncounter: boolean;
    setInEncounter: (inEncounter: boolean) => void;
    levelupValue: number | null;
    setLevelupValue: (levelupValue: number | null) => void;
    hidePollNotification: boolean;
    setHidePollNotification: (hidePollNotification: boolean) => void;
}

export const sessionStore = create<SessionState>()(
    persist(
        (set) => ({
            hideChat: false,
            setHideChat: (hideChat) => set(() => ({ hideChat })),
            mainChatRoom: { displayName: "Global Chat", room: { id: 1, name: "global" } },
            setMainChatRoom: (mainChatRoom) => set(() => ({ mainChatRoom })),
            inEncounter: false,
            setInEncounter: (inEncounter) => set(() => ({ inEncounter })),
            levelupValue: null,
            setLevelupValue: (levelupValue) => set(() => ({ levelupValue })),
            hidePollNotification: false,
            setHidePollNotification: (hidePollNotification) => set(() => ({ hidePollNotification })),
        }),
        {
            name: "sessionStore",
            storage: createJSONStorage(() => sessionStorage),
            version: 1.1,
        }
    )
);
