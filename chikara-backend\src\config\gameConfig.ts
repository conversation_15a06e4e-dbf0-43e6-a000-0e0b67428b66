import ms from "ms";
import { PrismaClient } from "@prisma/client";
import { LogErrorStack } from "../utils/log.js";
import { defaultDatabaseAdapter } from "./database.js";

const adapter = defaultDatabaseAdapter;

// Use base Prisma client to avoid circular dependency with extensions
const prisma = new PrismaClient({ adapter });

// In-memory cache for config values
const configCache = new Map<string, unknown>();
let cacheExpiry = 0;
const CACHE_TTL = 24 * 60 * 60 * 1000; // 1 day

// Database config loading with caching
async function loadConfigFromDB(): Promise<Record<string, unknown>> {
    const now = Date.now();

    // Return cached values if still valid
    if (configCache.size > 0 && now < cacheExpiry) {
        const cachedConfig: Record<string, unknown> = {};
        for (const [key, value] of configCache.entries()) {
            cachedConfig[key] = value;
        }
        return cachedConfig;
    }

    try {
        const dbConfigs = await prisma.game_config.findMany();

        // Clear and rebuild cache
        configCache.clear();
        const dbConfigMap: Record<string, unknown> = {};

        for (const config of dbConfigs) {
            configCache.set(config.key, config.value);
            dbConfigMap[config.key] = config.value;
        }

        cacheExpiry = now + CACHE_TTL;
        return dbConfigMap;
    } catch (error) {
        LogErrorStack({ error, message: "Failed to load config from database" });
        // Return existing cache if available, otherwise empty object
        return configCache.size > 0 ? Object.fromEntries(configCache) : {};
    }
}

// Refresh cache manually
export async function refreshConfigCache(): Promise<void> {
    cacheExpiry = 0; // Force cache expiry
    await loadConfigFromDB();
    await initializeGameConfig(); // Refresh runtime config and update exports
}

// AUTH //
const authConfig = {
    public: {
        REGISTRATION_DISABLED: false,
        LOGIN_DISABLED: false,
        DISCORD_AUTH_ENABLED: true,
        GOOGLE_AUTH_ENABLED: true,
    },
    hidden: {
        RESET_TOKEN_VALID_FOR: ms("10m"),
        SITE_MAINTENANCE_MODE: false,
        REGISTRATION_CHATMESSAGE_DISABLED: false,
    },
} as const;

// FRONTEND CONFIG //
const frontendConfig = {
    public: {
        MARQUEE_BANNER_DISABLED: false,
    },
    hidden: {},
} as const;

// BATTLE //
const battleConfig = {
    public: {
        PVP_MIN_LVL: 5,
        PVP_BATTLE_AP_COST: 2,
        ROOFTOP_BATTLE_AP_COST: 2,
        MUG_XP: 100,
        CRIPPLE_XP: 100,
        LEAVE_XP: 200,
        NPC_KILL_XP: 100,
        BOSS_KILL_XP: 200,
        DAILY_USER_ATTACK_LIMIT: 3,
        BASE_STAMINA: 100,
    },
    hidden: {
        BATTLE_TIMEOUT_MS: ms("10m"), // 10 mins
        FLEE_CHANCE: 0.4,
        BASE_JAIL_CHANCE: 0.3,
        JAIL_DURATION_MS: ms("10m"),
        BASE_MUG_AMOUNT: Math.random() * (0.5 - 0.3) + 0.3,
        BASE_STAMINA_REGEN: 5,
        DEFAULT_HOSPITAL_DURATION_MS: ms("10m"), // NPC loss/Leave
        MUG_HOSPITAL_DURATION_MS: ms("15m"),
        PVP_LOSS_HOSPITAL_DURATION_MS: ms("15m"),
        CRIPPLE_HOSPITAL_DURATION_MS: ms("30m"),
    },
} as const;

// USER //
const userConfig = {
    public: {
        MAX_LEVEL_CAP: 40,
        HEALING_TICK_INTERVAL: ms("1m"), // every minute
        AP_TICK_INTERVAL: ms("10m"), // every 10 minutes
        ENERGY_TICK_MS: ms("1m"), // 1 energy per minute
        HEALTH_REGEN_AMOUNT: 0.025, // 2.5% max hp
        // Focus system
        FOCUS_PER_BATTLE_WIN: 1, // Focus gained from winning battles
        FOCUS_PER_NPC_KILL: 1, // Focus gained from killing NPCs (same as battle win)
        FOCUS_PER_QUEST_COMPLETE: 5, // Focus gained from completing quests
        FOCUS_PER_DAILY_QUEST: 3, // Focus gained from daily quests
        FOCUS_PER_MISSION_HOUR: 1, // Focus gained per mission hour
        FOCUS_PER_EXPLORE_NODE: 3, // Focus gained from exploring nodes
        DAILY_FATIGUE_CAP: 200, // Maximum fatigue that can be spent per day
        FOCUS_TO_EXP_RATIO: 10, // 1 focus = 10 exp when training
        TALENT_RESPEC_BASE_COST: 1000,
        NEW_PLAYER_HOSPITAL_PROTECTION_LEVEL: 15,
    },
    hidden: {
        XP_TO_LEVEL_MULTIPLIER: 800,
    },
} as const;

// USER SKILLS (STATS) //
const skillsConfig = {
    public: {
        STAMINA_PER_ENDURANCE_LEVEL: 5,
        STA_STAT_EFFECT_MAX_COMBAT_STA_INCREASE: 5,
    },
    hidden: {},
} as const;

// BANK //
const bankConfig = {
    public: {
        DEPOSIT_DISABLED: false,
        BANK_DISABLED: false,
        TRANSACTION_FEE: 0.15,
        MINIMUM_DEPOSIT: 300,
        MINIMUM_WITHDRAWAL: 100,
        MINIMUM_TRANSFER: 100,
        TRANSACTION_HISTORY_LIMIT: 10,
    },
    hidden: {},
} as const;

// CASINO //
const casinoConfig = {
    public: { CASINO_DISABLED: false, LOTTERY_DISABLED: false, SLOTS_MAX_BET: 5_000_000, LOTTERY_TICKET_COST: 2500 },
    hidden: {},
} as const;

// JOBS //
const jobsConfig = {
    public: { JOBS_DISABLED: false },
    hidden: {},
} as const;

// LEADERBOARDS //
const leaderboardsConfig = {
    public: { LEADERBOARDS_DISABLED: false, USERS_PER_BOARD: 3 },
    hidden: {},
} as const;

// CRAFTING //
const craftingConfig = {
    public: { CRAFTING_ENERGY_COST: 0 },
    hidden: {},
} as const;

// CHAT //
const chatConfig = {
    public: { CHAT_DISABLED: false, CHAT_MESSAGE_SENDING_DISABLED: false },
    hidden: { MAX_CHAT_HISTORY_LENGTH: 300, DISCORD_CHAT_WEBHOOK_ENABLED: true },
} as const;

// NOTIFICATIONS //
const notificationConfig = {
    public: {},
    hidden: {
        MAX_NOTIFICATION_HISTORY_LENGTH: 300,
        AP_NOTIFICATIONS_ENABLED: true,
        ENERGY_NOTIFICATIONS_ENABLED: false,
        HEALTH_NOTIFICATIONS_ENABLED: true,
    },
} as const;

// UNIQUE ITEMS //
const uniqueItemsConfig = {
    public: {
        ANON_ITEM_NAME: "Balaclava",
        HOSPITALISE_ITEM_NAME: "Death Book",
        REVIVE_ITEM_NAME: "Life Book",
        JAIL_ITEM_NAME: "Kompromat",
        MEGAPHONE_ITEM_NAME: "Megaphone",
        GANG_CREATION_ITEM_NAME: "Gang Sigil",
        DAILY_CHEST_ITEM_ID: 232,
        RUNSHOES_ITEM_NAME: "Shoes of Quickness",
        WEALTH_RING_ITEM_NAME: "Ring of Wealth",
        SM_RAW_MATERIALS_ITEM_NAME: "Small Raw Materials Crate",
        SM_TOOLS_ITEM_NAME: "Small Tools Crate",
    },
    hidden: {
        // 30 mins
        DEATHNOTE_HOSPITAL_TIME_MS: ms("30m"),
        // 20 mins
        KOMPROMAT_JAIL_TIME_MS: ms("20m"),
    },
} as const;

// LEVEL GATES //
const levelGatesConfig = {
    public: {
        JOBS_LEVEL_GATE: 4,
        TALENTS_LEVEL_GATE: 5,
        CRAFTING_LEVEL_GATE: 7,
        SHOP1_LEVEL_GATE: 7,
        SHOP2_LEVEL_GATE: 15,
        SHOP3_LEVEL_GATE: 0,
        COURSES_LEVEL_GATE: 8,
        DAILY_QUESTS_LEVEL_GATE: 3,
        ROOFTOP_BATTLES_LEVEL_GATE: 10,
        MARKET_LEVEL_GATE: 12,
        ARCADE_LEVEL_GATE: 15,
    },
    hidden: {},
} as const;

// ROGUELIKE //
const roguelikeConfig = {
    public: {
        ROGUELIKE_DISABLED: false,
        ACTION_POINTS_REQUIRED: 1,
        CHURCH_MINIMUM_ZONE_LVL: 3,
        MALL_MINIMUM_ZONE_LVL: 7,
        SHRINE_MINIMUM_ZONE_LVL: 10,
        ALLEY_MINIMUM_ZONE_LVL: 16,
        SEWERS_MINIMUM_ZONE_LVL: 12,
    },
    hidden: {
        // 10 Mins
        DEFAULT_ENCOUNTER_HOSPITAL_DURATION_MS: ms("10m"),
        DEFAULT_ENCOUNTER_JAIL_DURATION_MS: ms("10m"),
        NORMAL_NPC_BATTLE_TIMEOUT_MS: ms("10m"),
        BOSS_BATTLE_TIMEOUT_MS: ms("10m"),
        SCAVENGE_TIMEOUT_MS: ms("5m"),
        MAX_NODES: 20,
        MIN_NODES: 18,
        DOUBLE_EDGE_CHANCE: 0.3,
        MAX_BUFF_VALUE_CAP: 1.5,
        BATTLE_WEIGHT: 9,
        BUFF_WEIGHT: 1,
        CHARACTER_WEIGHT: 5,
        SCAVENGE_NODE_WEIGHT: 4.5,
    },
} as const;

// CLASSES //
const classesConfig = {
    public: { CLASS_NAMES: ["Honoo", "Mizu", "Tsuchi", "Kaze"] },
    hidden: {},
} as const;

const registrationCodes = {
    public: { REGISTRATION_CODES_DISABLED: true },
    hidden: { REFERRAL_CODE_LIMIT: 3 },
} as const;

// TASKS/QUESTS //
const questsConfig = {
    public: { QUESTS_DISABLED: false },
    hidden: {
        QUEST_XP_REWARD_MULTIPLIER: 0.25,
        DAILY_QUEST_XP_REWARD_MULTIPLIER: 0.1,
    },
} as const;

// SHOPS //
const shopConfig = {
    public: { MAX_TRADER_REP: 3, SHOP_ITEM_COST_MULTIPLIER: 7, LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT: 4 },
    hidden: {},
} as const;

// BOUNTIES //
const bountyConfig = {
    public: {
        MIN_BOUNTY: 100,
        BOUNTY_FEE: 0.1,
        BOUNTY_MIN_LEVEL: 5,
        DEFAULT_STAFF_BOUNTY_USER_ID: 5,
        RANDOM_BOUNTY_AMOUNT: 50,
    },
    hidden: {
        RANDOM_BOUNTY_CHANCE: 0.2,
    },
} as const;

// PROFILE //
const profileConfig = {
    public: { PROFILE_COMMENT_MAX_LENGTH: 160 },
    hidden: {},
} as const;

// MISSIONS //
const missionConfig = {
    public: {
        MISSION_TIER_REQ_LEVELS: [4, 10, 15, 20, 25],
        MISSION_TIER_REQ_HOURS: [0, 15, 40, 80, 125],
        MISSION_DURATIONS_HOURS: [2, 4, 8],
    },
    hidden: {},
} as const;

const shrineConfig = {
    public: {
        SHRINE_DISABLED: false,
        SHRINE_MINIMUM_DONATION: 100,
    },
    hidden: {
        SHRINE_GOAL_CIRCULATING_PERCENT: 0.07,
    },
} as const;

const auctionConfig = {
    public: {
        ALLOWED_AUCTION_ITEM_TYPES: [
            "weapon",
            "offhand",
            "ranged",
            "shield",
            "head",
            "finger",
            "hands",
            "chest",
            "legs",
            "feet",
            "consumable",
            "junk",
            "special",
            "recipe",
            "crafting",
            "pet",
            "pet_food",
        ],
        BLACKLISTED_AUCTION_ITEM_TYPES: ["quest"],
        BLACKLISTED_AUCTION_ITEM_IDS: [232, 37, 22, 155, 250, 247, 248, 246, 107, 108, 244],
    },
    hidden: {},
} as const;

const gangConfig = {
    public: {
        DAILY_ESSENCE_CAP: 100,
    },
    hidden: {
        ELO_K_FACTOR: 32,
        BASE_LIFE_ESSENCE_REWARD: 5,
        BASE_RESPECT_REWARD: 25,
    },
} as const;

const infirmaryConfig = {
    public: {
        MINOR_COST_PER_LEVEL: 125,
        MODERATE_COST_PER_LEVEL: 250,
        SEVERE_COST_PER_LEVEL: 450,
        COST_PER_HP: 4,
    },
    hidden: {},
} as const;

const petConfig = {
    public: {
        EGG_TIME_PER_PROGRESS_POINT: ms("1h"),
    },
    hidden: {},
} as const;

// Define the main configs object
const configs = {
    authConfig,
    frontendConfig,
    battleConfig,
    bankConfig,
    chatConfig,
    craftingConfig,
    casinoConfig,
    userConfig,
    jobsConfig,
    notificationConfig,
    levelGatesConfig,
    leaderboardsConfig,
    roguelikeConfig,
    uniqueItemsConfig,
    classesConfig,
    registrationCodes,
    questsConfig,
    shopConfig,
    skillsConfig,
    bountyConfig,
    profileConfig,
    missionConfig,
    shrineConfig,
    auctionConfig,
    gangConfig,
    infirmaryConfig,
    petConfig,
} as const;

type UnionToIntersection<U> = (U extends unknown ? (k: U) => void : never) extends (k: infer I) => void ? I : never;

interface ConfigSection {
    public: Record<string, unknown>;
    hidden: Record<string, unknown>;
}
type ConfigsInput = typeof configs;

type AllMergedSections<T extends Record<string, unknown>> = {
    [K in keyof T]: T[K] extends ConfigSection ? T[K]["public"] & T[K]["hidden"] : never;
}[keyof T];

type PreciseFlatConfigType<T extends Record<string, unknown>> = {} & UnionToIntersection<AllMergedSections<T>>;

function createFlatConfig<T extends ConfigsInput>(cfg: T): PreciseFlatConfigType<T> {
    const flattened = Object.entries(cfg).reduce(
        (acc, [, value]) => {
            const section = value as ConfigSection;
            Object.assign(acc, section.public, section.hidden);
            return acc;
        },
        {} as Record<string, unknown>
    );

    return flattened as PreciseFlatConfigType<T>;
}

async function createFlatConfigWithDB<T extends ConfigsInput>(cfg: T): Promise<PreciseFlatConfigType<T>> {
    const hardcodedConfig = createFlatConfig(cfg);

    try {
        const dbConfig = await loadConfigFromDB();
        // Merge database config over hardcoded values
        const mergedConfig = { ...hardcodedConfig, ...dbConfig };
        return mergedConfig as PreciseFlatConfigType<T>;
    } catch (error) {
        LogErrorStack({ error, message: "Failed to load DB config, using hardcoded values" });
        return hardcodedConfig;
    }
}

type AllPublicSections<T extends Record<string, unknown>> = {
    [K in keyof T]: T[K] extends ConfigSection ? T[K]["public"] : never;
}[keyof T];

type PrecisePublicConfigType<T extends Record<string, unknown>> = {} & UnionToIntersection<AllPublicSections<T>>;

function createPublicConfig<T extends ConfigsInput>(cfg: T): PrecisePublicConfigType<T> {
    const flattened = Object.entries(cfg).reduce(
        (acc, [, value]) => {
            const section = value as ConfigSection;
            Object.assign(acc, section.public);
            return acc;
        },
        {} as Record<string, unknown>
    );

    return flattened as PrecisePublicConfigType<T>;
}

async function createPublicConfigWithDB<T extends ConfigsInput>(cfg: T): Promise<PrecisePublicConfigType<T>> {
    const hardcodedConfig = createPublicConfig(cfg);

    try {
        // Use the cached config data from loadConfigFromDB
        const allDbConfig = await loadConfigFromDB();

        // Filter to only include public values by checking the database for isPublic flag
        const publicDbConfigs = await prisma.game_config.findMany({
            where: { isPublic: true },
            select: { key: true }, // Only need the keys to filter
        });

        const publicKeys = new Set(publicDbConfigs.map((config) => config.key));
        const publicDbConfig: Record<string, unknown> = {};

        // Filter the cached config to only include public keys
        for (const [key, value] of Object.entries(allDbConfig)) {
            if (publicKeys.has(key)) {
                publicDbConfig[key] = value;
            }
        }

        // Merge database config over hardcoded values
        const mergedConfig = { ...hardcodedConfig, ...publicDbConfig };
        return mergedConfig as PrecisePublicConfigType<T>;
    } catch (error) {
        LogErrorStack({ error, message: "Failed to load DB public config, using hardcoded values" });
        return hardcodedConfig;
    }
}

// Export individual configs
export {
    authConfig,
    frontendConfig,
    battleConfig,
    bankConfig,
    chatConfig,
    craftingConfig,
    casinoConfig,
    userConfig,
    jobsConfig,
    notificationConfig,
    levelGatesConfig,
    leaderboardsConfig,
    roguelikeConfig,
    uniqueItemsConfig,
    classesConfig,
    registrationCodes,
    questsConfig,
    shopConfig,
    skillsConfig,
    bountyConfig,
    profileConfig,
    missionConfig,
    shrineConfig,
    auctionConfig,
    gangConfig,
    infirmaryConfig,
    petConfig,
};

export const gameConfig = configs;

// Runtime config cache - populated at server startup
let runtimeFlatConfig: PreciseFlatConfigType<typeof configs>;
let runtimePublicConfig: PrecisePublicConfigType<typeof configs>;

// Initialize with hardcoded fallback
runtimeFlatConfig = createFlatConfig(configs);
runtimePublicConfig = createPublicConfig(configs);

// Initialize configs from database (called at server startup)
export async function initializeGameConfig(): Promise<void> {
    try {
        const dbFlatConfig = await createFlatConfigWithDB(configs);
        const dbPublicConfig = await createPublicConfigWithDB(configs);

        runtimeFlatConfig = dbFlatConfig;
        runtimePublicConfig = dbPublicConfig;

        // Update the exported objects so destructuring works correctly
        updateExportedConfigs();
    } catch (error) {
        LogErrorStack({ error, message: "Failed to load game config from database, using hardcoded fallback" });
    }
}

// Create mutable objects that will be updated when database values are loaded
const flatConfig: PreciseFlatConfigType<typeof configs> = { ...runtimeFlatConfig };
const publicConfigObj: PrecisePublicConfigType<typeof configs> = { ...runtimePublicConfig };

// Function to update the exported objects with new values
function updateExportedConfigs() {
    // Clear existing properties
    for (const key of Object.keys(flatConfig)) {
        delete flatConfig[key as keyof typeof flatConfig];
    }
    for (const key of Object.keys(publicConfigObj)) {
        delete publicConfigObj[key as keyof typeof publicConfigObj];
    }

    // Add new properties
    Object.assign(flatConfig, runtimeFlatConfig);
    Object.assign(publicConfigObj, runtimePublicConfig);
}

export const publicConfig = publicConfigObj;
export default flatConfig;

// Async functions for getting fresh config from DB (for admin/management use)
export async function getFreshFlatConfig(): Promise<PreciseFlatConfigType<typeof configs>> {
    return await createFlatConfigWithDB(configs);
}

// Set a config value in the database
export async function setConfigValue(key: string, value: unknown, category?: string, isPublic = true): Promise<void> {
    try {
        await prisma.game_config.upsert({
            where: { key },
            update: {
                value: value as any,
                category,
                isPublic,
                updatedAt: new Date(),
            },
            create: {
                key,
                value: value as any,
                category,
                isPublic,
            },
        });

        // Invalidate cache and refresh runtime config
        cacheExpiry = 0;
        await initializeGameConfig();
    } catch (error) {
        LogErrorStack({ error, message: `Failed to set config value for key "${key}"` });
        throw error;
    }
}
