import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Home, DollarSign, Package, TrendingUp, RefreshCw, Calendar, Sparkles, MapPin, Bed, Shield } from 'lucide-react';
import { useGetHousingList } from '@/features/property/api/useGetHousingList';
import { useGetUserProperties } from '@/features/property/api/useGetUserProperties';
import { usePurchaseProperty, useSellProperty } from '@/features/property/api/usePropertyMutations';
import useFetchCurrentUser from '@/hooks/api/useFetchCurrentUser';
import type { Property, UserProperty } from '@/features/property/types/property';

type TabType = 'owned' | 'available';

const PropertyApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('owned');
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  
  // API Hooks
  const { data: properties, isLoading: loadingProperties } = useGetHousingList();
  const { data: userProperties, isLoading: loadingUserProperties } = useGetUserProperties();
  const { data: user } = useFetchCurrentUser();
  const purchaseProperty = usePurchaseProperty();
  const sellProperty = useSellProperty();

  const handlePurchase = (propertyId: number) => {
    purchaseProperty.mutate({ propertyId });
  };

  const handleSell = (propertyId: number) => {
    if (confirm('Are you sure you want to sell this property? You will receive 20% of the original cost.')) {
      sellProperty.mutate({ propertyId });
    }
  };

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`;
  };

  // Filter out properties the user already owns
  const availableProperties = properties?.filter(
    (property) => !userProperties?.some((up) => up.property.id === property.id)
  ) || [];

  // Get existing property of same type for replacement calculations
  const getExistingPropertyOfSameType = (propertyType: string) => {
    if (!userProperties) return undefined;
    const existing = userProperties.find((up) => up.property.propertyType === propertyType);
    return existing ? existing.property : undefined;
  };

  const renderOwnedProperties = () => {
    if (loadingUserProperties) {
      return (
        <div className="flex justify-center items-center py-16">
          <div className="loading loading-spinner loading-lg text-indigo-400"></div>
        </div>
      );
    }

    if (!userProperties || userProperties.length === 0) {
      return (
        <div className="text-center py-16">
          <Home className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">You don't own any properties yet</p>
          <p className="text-gray-500 text-sm mt-1">Check the available properties to purchase!</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {userProperties.map((userProperty) => {
          const { property } = userProperty;
          const sellPrice = Math.floor(property.cost * 0.2);

          return (
            <motion.div
              key={userProperty.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-gray-800 rounded-lg p-4 border border-gray-700"
            >
              <div className="flex items-start gap-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-500/20">
                  <Home className="h-6 w-6 text-indigo-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium mb-1">{property.name}</h3>
                  <p className="text-gray-400 text-sm mb-3">{property.description}</p>

                  {/* Property Stats */}
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    <div className="flex items-center gap-2 text-sm">
                      <DollarSign className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">Value: {formatCurrency(property.cost)}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Package className="w-4 h-4 text-blue-400" />
                      <span className="text-gray-300">{property.slots} slots</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm col-span-2">
                      <Calendar className="w-4 h-4 text-purple-400" />
                      <span className="text-gray-300">
                        Purchased: {new Date(userProperty.purchaseDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  {/* Property Buffs */}
                  {property.buffs && Object.keys(property.buffs).length > 0 && (
                    <div className="mb-3">
                      <p className="text-xs text-gray-500 mb-1">Active Buffs:</p>
                      <div className="flex flex-wrap gap-1">
                        {Object.entries(property.buffs).map(([buff, value]) => (
                          <span
                            key={buff}
                            className="px-2 py-0.5 text-xs bg-blue-500/20 text-blue-400 rounded-full"
                          >
                            {buff}: {typeof value === 'number' 
                              ? `+${((value - 1) * 100).toFixed(0)}%` 
                              : String(value)}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleSell(property.id)}
                      disabled={sellProperty.isPending}
                      className="px-3 py-1 bg-red-600/80 hover:bg-red-600 text-white text-sm rounded-lg transition-colors flex items-center gap-1"
                    >
                      <DollarSign className="w-4 h-4" />
                      Sell for {formatCurrency(sellPrice)}
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    );
  };

  const renderAvailableProperties = () => {
    if (loadingProperties) {
      return (
        <div className="flex justify-center items-center py-16">
          <div className="loading loading-spinner loading-lg text-indigo-400"></div>
        </div>
      );
    }

    if (!availableProperties || availableProperties.length === 0) {
      return (
        <div className="text-center py-16">
          <Home className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No properties available</p>
          <p className="text-gray-500 text-sm mt-1">You already own all available properties!</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {availableProperties.map((property, index) => {
          const existingProperty = getExistingPropertyOfSameType(property.propertyType);
          const isReplacement = !!existingProperty;
          const sellValue = existingProperty ? Math.floor(existingProperty.cost * 0.2) : 0;
          const netCost = isReplacement ? property.cost - sellValue : property.cost;
          const canAfford = (user?.cash || 0) >= netCost;

          return (
            <motion.div
              key={property.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="bg-gray-800 rounded-lg p-4 border border-gray-700"
            >
              <div className="flex items-start gap-3">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-500/20">
                  <Home className="h-6 w-6 text-indigo-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium mb-1">{property.name}</h3>
                  <p className="text-gray-400 text-sm mb-3">{property.description}</p>

                  {/* Property Info */}
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    <div className="flex items-center gap-2 text-sm">
                      <DollarSign className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">
                        {isReplacement ? 'Net Cost' : 'Price'}: {formatCurrency(netCost)}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Package className="w-4 h-4 text-blue-400" />
                      <span className="text-gray-300">{property.slots} slots</span>
                    </div>
                  </div>

                  {/* Replacement Warning */}
                  {isReplacement && (
                    <div className="bg-orange-500/10 border border-orange-500/30 rounded-lg p-2 mb-3">
                      <div className="flex items-center gap-2 text-sm">
                        <RefreshCw className="w-4 h-4 text-orange-400" />
                        <span className="text-orange-400">
                          Replaces your {existingProperty.name} (Trade-in: {formatCurrency(sellValue)})
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Property Buffs Preview */}
                  {property.buffs && Object.keys(property.buffs).length > 0 && (
                    <div className="mb-3">
                      <p className="text-xs text-gray-500 mb-1">Property Buffs:</p>
                      <div className="flex flex-wrap gap-1">
                        {Object.entries(property.buffs).map(([buff, value]) => (
                          <span
                            key={buff}
                            className="px-2 py-0.5 text-xs bg-green-500/20 text-green-400 rounded-full"
                          >
                            {buff}: {typeof value === 'number' 
                              ? `+${((value - 1) * 100).toFixed(0)}%` 
                              : String(value)}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Purchase Button */}
                  <button
                    onClick={() => handlePurchase(property.id)}
                    disabled={!canAfford || purchaseProperty.isPending}
                    className={`w-full py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 ${
                      canAfford
                        ? 'bg-indigo-600 hover:bg-indigo-700 text-white'
                        : 'bg-gray-700 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {canAfford ? (
                      <>
                        <TrendingUp className="w-4 h-4" />
                        Purchase Property
                      </>
                    ) : (
                      <>
                        <DollarSign className="w-4 h-4" />
                        Insufficient Funds
                      </>
                    )}
                  </button>

                  {/* Original Price if replacement */}
                  {isReplacement && (
                    <p className="text-xs text-gray-500 text-center mt-2">
                      Original price: {formatCurrency(property.cost)}
                    </p>
                  )}
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-900 to-purple-900 p-4">
        <div className="flex items-center gap-3 mb-2">
          <div className="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center">
            <Home className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1">
            <h2 className="text-xl font-bold text-white">Property Manager</h2>
            <p className="text-indigo-200 text-sm">Manage your real estate portfolio</p>
          </div>
          <div className="text-right">
            <p className="text-indigo-200 text-xs">Your Balance</p>
            <p className="text-white font-bold">{formatCurrency(user?.cash || 0)}</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-gray-800 p-2 flex gap-2 border-b border-gray-700">
        {[
          { id: 'owned' as TabType, label: 'My Properties', icon: Shield },
          { id: 'available' as TabType, label: 'Available', icon: Sparkles },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center gap-2 ${
              activeTab === tab.id
                ? 'bg-gray-700 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span className="text-sm">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'owned' && renderOwnedProperties()}
        {activeTab === 'available' && renderAvailableProperties()}
      </div>
    </div>
  );
};

export default PropertyApp;
