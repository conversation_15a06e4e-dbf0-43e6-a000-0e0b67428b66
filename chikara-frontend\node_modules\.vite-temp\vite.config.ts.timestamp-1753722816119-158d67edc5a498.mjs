// vite.config.ts
import path from "path";
import tailwindcss from "file:///C:/Users/<USER>/Documents/Github/chikara-academy/node_modules/.bun/@tailwindcss+vite@4.1.11+cd882782e9191937/node_modules/@tailwindcss/vite/dist/index.mjs";
import react from "file:///C:/Users/<USER>/Documents/Github/chikara-academy/node_modules/.bun/@vitejs+plugin-react@4.7.0+cd882782e9191937/node_modules/@vitejs/plugin-react/dist/index.js";
import { defineConfig } from "file:///C:/Users/<USER>/Documents/Github/chikara-academy/node_modules/.bun/vite@7.0.6/node_modules/vite/dist/node/index.js";
import { VitePWA } from "file:///C:/Users/<USER>/Documents/Github/chikara-academy/node_modules/.bun/vite-plugin-pwa@1.0.2+cd882782e9191937/node_modules/vite-plugin-pwa/dist/index.js";

// package.json
var package_default = {
  name: "chikara-frontend",
  version: "0.16.0",
  private: true,
  type: "module",
  scripts: {
    dev: "vite --host",
    start: "vite",
    build: "vite build",
    test: "vitest",
    "test:watch": "vitest watch",
    "test:visual": "playwright test --config=playwright/playwright.config.ts",
    "test:visual:update": "playwright test --config=playwright/playwright.config.ts --update-snapshots",
    lint: "eslint --fix --ext .js,.jsx,.ts,.tsx,.json src",
    format: "prettier --write .",
    "type-check": "tsc --noEmit"
  },
  dependencies: {
    "@date-fns/utc": "^2.1.0",
    "@formkit/auto-animate": "^0.8.2",
    "@headlessui/react": "^2.2.4",
    "@orpc/client": "^1.7.4",
    "@orpc/tanstack-query": "^1.7.4",
    "@radix-ui/react-separator": "^1.1.7",
    "@radix-ui/react-switch": "^1.2.5",
    "@sentry/browser": "^9.40.0",
    "@sentry/react": "^9.40.0",
    "@tailwindcss/aspect-ratio": "^0.4.2",
    "@tailwindcss/typography": "^0.5.16",
    "@tailwindcss/vite": "^4.1.11",
    "@tanstack/react-query": "^5.83.0",
    "@unpic/react": "^1.0.1",
    "@vitejs/plugin-react": "^4.7.0",
    "ag-grid-community": "^34.0.2",
    "ag-grid-react": "^34.0.2",
    axios: "^1.10.0",
    "better-auth": "^1.3.2",
    "chart.js": "^4.5.0",
    "chartjs-plugin-datalabels": "^2.2.0",
    "class-variance-authority": "^0.7.1",
    clsx: "^2.1.1",
    daisyui: "^5.0.46",
    "date-fns": "^4.1.0",
    elkjs: "^0.10.0",
    "embla-carousel-react": "^8.6.0",
    firebase: "^12.0.0",
    "framer-motion": "^12.23.6",
    "json-with-bigint": "^3.4.4",
    "lucide-react": "^0.525.0",
    "posthog-js": "^1.257.0",
    "radix-ui": "^1.4.2",
    react: "^19.1.0",
    "react-chartjs-2": "^5.3.0",
    "react-countdown": "^2.3.6",
    "react-cropper": "^2.3.3",
    "react-dom": "^19.1.0",
    "react-hot-toast": "^2.5.2",
    "react-portal": "^4.3.0",
    "react-quiz-component": "^0.9.1",
    "react-router-dom": "^7.7.0",
    "react-slot-counter": "^3.3.1",
    "react-string-replace": "^1.1.1",
    "react-tooltip": "^5.29.1",
    "react-tracked": "^2.0.1",
    reactflow: "^11.11.4",
    "socket.io-client": "^4.8.1",
    "survey-core": "^2.2.5",
    "survey-react-ui": "^2.2.5",
    "tailwind-merge": "^3.3.1",
    "tailwind-variants": "^1.0.0",
    tailwindcss: "^4.1.11",
    vaul: "^1.1.2",
    vite: "^7.0.5",
    zustand: "^5.0.6"
  },
  eslintConfig: {
    extends: [
      "react-app"
    ]
  },
  browserslist: [
    "defaults",
    "not dead"
  ],
  devDependencies: {
    "@eslint/js": "^9.31.0",
    "@playwright/test": "^1.54.1",
    "@sentry/vite-plugin": "^4.0.0",
    "@tailwindcss/forms": "^0.5.10",
    "@tanstack/eslint-plugin-query": "^5.81.2",
    "@tanstack/react-query-devtools": "^5.83.0",
    "@testing-library/jest-dom": "^6.6.3",
    "@testing-library/react": "^16.3.0",
    "@types/node": "^24.0.15",
    "@types/react": "^19.1.8",
    "@types/react-dom": "^19.1.6",
    "@types/react-portal": "^4.0.7",
    "@typescript/native-preview": "^7.0.0-dev.20250712.1",
    "babel-plugin-react-compiler": "19.1.0-rc.2",
    eslint: "^9.31.0",
    "eslint-import-resolver-typescript": "^4.4.4",
    "eslint-plugin-import-x": "^4.16.1",
    "eslint-plugin-react": "^7.37.5",
    "eslint-plugin-react-hooks": "6.1.0-canary-97cdd5d3-20250710",
    "eslint-plugin-react-refresh": "^0.4.20",
    "eslint-plugin-tailwindcss": "^3.18.2",
    "eslint-plugin-unicorn": "^59.0.1",
    globals: "^16.3.0",
    jsdom: "^26.1.0",
    typescript: "^5.8.3",
    "typescript-eslint": "^8.37.0",
    "vite-plugin-pwa": "^1.0.1",
    vitest: "^3.2.4",
    "workbox-core": "^7.3.0",
    "workbox-precaching": "^7.3.0",
    "workbox-window": "^7.3.0"
  }
};

// vite.config.ts
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\Documents\\Github\\chikara-academy\\chikara-frontend";
var ReactCompilerConfig = {
  sources: (filename) => {
    return filename.indexOf("src/") !== -1 && !filename.endsWith("App.tsx") && !filename.includes("/test/");
  }
};
var vite_config_default = ({ mode }) => {
  const isDevelopment = mode === "development";
  return defineConfig({
    server: {
      port: 8080
    },
    build: {
      sourcemap: false
      // Source maps on for Sentry
    },
    plugins: [
      tailwindcss(),
      react({
        babel: {
          plugins: [["babel-plugin-react-compiler", ReactCompilerConfig]]
        }
      }),
      VitePWA({
        strategies: "injectManifest",
        injectManifest: {
          globPatterns: ["**/*"],
          maximumFileSizeToCacheInBytes: 3145728
          // 3 MiB
        },
        registerType: "autoUpdate",
        srcDir: "./service-worker",
        filename: "firebase-messaging-sw.js",
        workbox: {
          maximumFileSizeToCacheInBytes: 3145728
          // 3 MiB
        },
        manifest: {
          name: "Chikara Battle Academy",
          short_name: "Chikara Academy",
          description: "Chikara Battle Academy MMORPG",
          theme_color: "#07072e",
          background_color: "#07072e",
          start_url: "/",
          icons: [
            {
              src: "./logo512.png",
              sizes: "512x512",
              type: "image/png"
            },
            {
              src: "./logo192.png",
              sizes: "192x192",
              type: "image/png",
              purpose: "maskable"
            }
          ],
          orientation: "portrait",
          display: "standalone",
          id: "chikaraacademy",
          categories: ["games"]
        }
      })
      // sentryVitePlugin({
      //     org: "clearwater-games",
      //     project: "javascript-react",
      //     disable: isDevelopment,
      //     authToken: "490432d509284b86a95c8a784fca9d68217853ce2baa4710b12220d25097ccae",
      //     sourcemaps: {
      //         // Specify the directory containing build artifacts
      //         assets: "./dist/**",
      //         // Don't upload the source maps of dependencies
      //         ignore: ["./node_modules/**"],
      //     },
      //     // Helps troubleshooting - set to false to make plugin less noisy
      //     debug: false,
      //     // Optionally uncomment the line below to override automatic release name detection
      //     // release: env.RELEASE,
      // }),
    ],
    define: {
      "process.env.NODE_ENV": `"${mode}"`,
      "import.meta.env.VITE_PACKAGE_VERSION": JSON.stringify(package_default.version)
    },
    resolve: {
      alias: {
        "@": path.resolve(__vite_injected_original_dirname, "./src")
      }
    },
    test: {
      globals: true,
      environment: "jsdom",
      setupFiles: ["./src/test/setup.ts"],
      css: true,
      include: ["src/**/*.{test,spec}.{ts,mts,tsx}"],
      exclude: ["**/node_modules/**", "**/dist/**", "**/.{idea,git,cache,output,temp}/**"],
      clearMocks: true,
      restoreMocks: true,
      mockReset: true,
      isolate: true
    }
  });
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
