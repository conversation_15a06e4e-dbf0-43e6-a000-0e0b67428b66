import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import missingIcon from "@/assets/images/missingIcon.png";
import Button from "@/components/Buttons/Button";
import { DisplayItem } from "@/components/DisplayItem";
import type { ExploreNodeLocation } from "../types/explore.types";
import StatusEffects from "@/components/StatusEffects";
import { rarityColours } from "@/helpers/rarityColours";
import { sceneManager } from "@/helpers/sceneManager";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import type { Item } from "@/types/item";
import useExploreForagingOperation from "../api/useExploreForagingOperation";
import { ExploreViewModal } from "./ExploreViewModal";

interface ForagingViewProps {
    nodeId: number;
    location: ExploreNodeLocation;
    foragingType: string;
    difficulty: "easy" | "medium" | "hard";
    onClose: () => void;
}

interface ForagingResult {
    success: boolean;
    message: string;
    itemReward?: Item;
    itemQuantity?: number;
    injury?: {
        name: string;
        description: string;
    };
}

// Constants

const FORAGING_ICONS: Record<string, string> = {
    herbs: "https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png",
    mushrooms: "https://img.icons8.com/?size=100&id=43138&format=png",
    berries: "https://img.icons8.com/?size=100&id=bqO7Szu38UJF&format=png",
    flowers: "https://img.icons8.com/?size=100&id=d6K0u3dO9c4C&format=png",
    roots: "https://img.icons8.com/?size=100&id=C3GJQJl8Dqjp&format=png",
    medicinal: "https://img.icons8.com/?size=100&id=vB6PoShzdZKw&format=png",
    plants: "https://img.icons8.com/?size=100&id=ecZ41Q9R6SIW&format=png",
};

const DIFFICULTY_CONFIG = {
    easy: {
        color: "text-green-400",
        bgColor: "bg-green-500/20 border-green-400/30",
        icon: "🌱",
        label: "Easy",
        description: "Gentle gathering, common finds",
    },
    medium: {
        color: "text-yellow-400",
        bgColor: "bg-yellow-500/20 border-yellow-400/30",
        icon: "🌿",
        label: "Medium",
        description: "Careful foraging, rarer plants",
    },
    hard: {
        color: "text-red-400",
        bgColor: "bg-red-500/20 border-red-400/30",
        icon: "🍄",
        label: "Hard",
        description: "Dangerous terrain, exotic species",
    },
};

// Utility functions
const getForagingIcon = (foragingType: string): string =>
    FORAGING_ICONS[foragingType] || FORAGING_ICONS.herbs || missingIcon;

// Custom hooks
const useForagingQueries = () => {
    const queryClient = useQueryClient();

    const invalidateQueries = async () => {
        await Promise.all([
            queryClient.invalidateQueries({ queryKey: api.explore.getMapByLocation.key() }),
            queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() }),
        ]);
    };

    return { invalidateQueries };
};

// Status Icon Component
const StatusIcon = ({ isSuccess }: { isSuccess: boolean }) => {
    const iconClass = isSuccess ? "text-green-400" : "text-red-400";
    const bgClass = isSuccess ? "bg-green-500/20" : "bg-red-500/20";

    return (
        <div className={cn("w-16 h-16 md:w-20 md:h-20 mx-auto rounded-full flex items-center justify-center", bgClass)}>
            <svg
                className={cn("w-8 h-8 md:w-10 md:h-10", iconClass)}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
            >
                {isSuccess ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                )}
            </svg>
        </div>
    );
};

// Item Reward Component
const ItemReward = ({ item, quantity }: { item: Item; quantity?: number }) => (
    <div className="bg-slate-700/50 rounded-xl p-4 md:p-6 border border-indigo-400/30">
        <div className="flex items-center justify-center gap-3 md:gap-4">
            <span className="text-white text-lg md:text-xl font-semibold">{quantity ?? 1}x</span>
            <DisplayItem item={item} height="h-10 w-auto md:h-12 lg:h-14" className="inline-block" />
            <span className={cn("text-lg md:text-xl font-semibold", rarityColours(item.rarity))}>{item.name}</span>
        </div>
    </div>
);

// Foraging Action Button Component
const ForagingActionButton = ({
    foragingType,
    difficulty,
    onStartForaging,
    isDisabled,
}: {
    foragingType: string;
    difficulty: "easy" | "medium" | "hard";
    onStartForaging: () => void;
    isDisabled: boolean;
}) => {
    const difficultyConfig = DIFFICULTY_CONFIG[difficulty];

    return (
        <button
            type="button"
            disabled={isDisabled}
            className={cn(
                "cursor-pointer group relative overflow-hidden rounded-xl bg-gradient-to-br from-green-600 to-emerald-700",
                "hover:from-green-500 hover:to-emerald-600 transition-all duration-300",
                "border-2 border-green-400/30 hover:border-green-400/60",
                "shadow-lg hover:shadow-xl transform hover:scale-[1.02]",
                "disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none",
                "p-6 md:p-8 w-full max-w-md mx-auto"
            )}
            onClick={onStartForaging}
        >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative flex flex-col items-center gap-4">
                <div className="flex items-center gap-4">
                    <img
                        src={getForagingIcon(foragingType)}
                        className="w-12 h-12 md:w-16 md:h-16 object-contain"
                        alt={`${foragingType} foraging`}
                    />
                    <div className="text-left">
                        <h3 className="text-white font-bold text-lg md:text-xl capitalize">Gather {foragingType}</h3>
                        <div className={cn("text-sm font-medium", difficultyConfig.color)}>
                            {difficultyConfig.icon} {difficultyConfig.label}
                        </div>
                    </div>
                </div>
                <p className="text-gray-200 text-sm text-center">{difficultyConfig.description}</p>
            </div>
        </button>
    );
};

// Result Panel Component
const ResultPanel = ({
    foragingResult,
    onAction,
    isLoading,
    isMobile,
    actionButtonText,
}: {
    foragingResult: ForagingResult;
    onAction: () => void;
    isLoading: boolean;
    isMobile: number | boolean;
    actionButtonText: string;
}) => (
    <div className="flex flex-col items-center gap-6 md:gap-8 text-center">
        <div className="space-y-4">
            <StatusIcon isSuccess={foragingResult.success} />
            <p className="text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-2xl">
                {foragingResult.message}
            </p>
        </div>

        {/* Item Reward for Success */}
        {foragingResult.success && foragingResult.itemReward && (
            <ItemReward item={foragingResult.itemReward} quantity={foragingResult.itemQuantity} />
        )}

        {/* Status Effects for Failure */}
        {!foragingResult.success && foragingResult.injury && (
            <div className="bg-slate-700/50 rounded-xl p-4 md:p-6 border border-red-400/30">
                <StatusEffects
                    currentEffects={[{ id: 1, count: 1, debuff: foragingResult.injury }]}
                    className="justify-center"
                />
            </div>
        )}

        <div className="w-full max-w-sm">
            <Button
                variant="primary"
                size={isMobile ? "md" : "lg"}
                isLoading={isLoading}
                className="w-full text-base md:text-lg"
                onClick={onAction}
            >
                {actionButtonText}
            </Button>
        </div>
    </div>
);

export const ForagingView = ({ nodeId, foragingType, difficulty, onClose }: ForagingViewProps) => {
    const [isLoading, setIsLoading] = useState(false);
    const [foragingResult, setForagingResult] = useState<ForagingResult | null>(null);
    const isMobile = useCheckMobileScreen();
    const { invalidateQueries } = useForagingQueries();

    const { mutate: processForagingOperation, isPending: isProcessingForaging } = useExploreForagingOperation();
    const sceneImage = sceneManager("citystreet1"); // You might want to add foraging-specific scenes

    const handleStartForaging = () => {
        if (isProcessingForaging) return;

        processForagingOperation(
            { nodeId },
            {
                onSuccess: (response) => {
                    if (!response) {
                        setForagingResult({
                            success: false,
                            message: "No response received from server",
                        });
                        return;
                    }
                    setForagingResult({
                        success: response.success || false,
                        message: response.message || "Foraging operation completed",
                        itemReward: response.itemReward as Item,
                        itemQuantity: response.itemQuantity,
                        injury: response.injury,
                    });
                },
                onError: (error) => {
                    setForagingResult({
                        success: false,
                        message: error?.message || "Something went wrong during foraging",
                    });
                },
            }
        );
    };

    const handleClose = async () => {
        setIsLoading(true);
        await invalidateQueries();
        onClose();
        setIsLoading(false);
    };

    const difficultyConfig = DIFFICULTY_CONFIG[difficulty];

    const subtitle = (
        <div
            className={cn(
                "px-3 py-1 rounded-full inline-block",
                difficultyConfig.bgColor
            )}
        >
            <span className={cn("text-sm font-medium", difficultyConfig.color)}>
                {difficultyConfig.icon} {difficultyConfig.label} Difficulty
            </span>
        </div>
    );

    return (
        <ExploreViewModal
            title="Foraging Expedition"
            subtitle={subtitle}
            borderColor="border-green-500/80"
            headerBorderColor="border-green-500/30"
            titleColor="text-green-400"
            backgroundImage={sceneImage || ""}
        >
            {foragingResult ? (
                <div className="flex-1 flex flex-col justify-center">
                    <ResultPanel
                        foragingResult={foragingResult}
                        isLoading={isLoading}
                        isMobile={isMobile}
                        actionButtonText="Return to Explore"
                        onAction={handleClose}
                    />
                </div>
            ) : (
                <div className="flex-1 flex flex-col justify-center gap-6 md:gap-8">
                    <div className="text-center">
                        <p className="text-gray-200 text-base md:text-lg lg:text-xl leading-relaxed max-w-3xl mx-auto">
                            You&apos;ve found a promising foraging site rich with{" "}
                            <span className="text-green-400 font-semibold capitalize">
                                {foragingType}
                            </span>
                            . Careful searching may yield valuable natural materials, but watch out for
                            hazards.
                        </p>
                    </div>

                    {isProcessingForaging ? (
                        <div className="text-center">
                            <div className="inline-flex items-center gap-2 text-gray-300 text-base md:text-lg">
                                <div className="size-6 border-2 border-green-400 border-t-transparent rounded-full animate-spin" />
                                <span>Foraging in progress...</span>
                            </div>
                        </div>
                    ) : (
                        <ForagingActionButton
                            foragingType={foragingType}
                            difficulty={difficulty}
                            isDisabled={isProcessingForaging}
                            onStartForaging={handleStartForaging}
                        />
                    )}
                </div>
            )}
        </ExploreViewModal>
    );
};
