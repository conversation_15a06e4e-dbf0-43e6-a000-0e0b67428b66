import React from "react";
import { <PERSON>ertCircle, RefreshCw, WifiOff, ServerCrash, ShieldAlert } from "lucide-react";
import { cn } from "@/lib/utils";

interface QueryErrorProps {
    error: Error | null;
    onRetry?: () => void;
    className?: string;
    showDetails?: boolean;
    /**
     * Whether the query is currently refetching after an error
     */
    isRefetching?: boolean;
    /**
     * Custom error message to display instead of the default
     */
    customMessage?: string;
    /**
     * Size variant for the error display
     */
    size?: "sm" | "md" | "lg";
}

interface ErrorDetails {
    icon: React.ElementType;
    iconColor: string;
    title: string;
    message: string;
    canRetry: boolean;
}

/**
 * Analyzes the error to provide appropriate UI details
 */
function getErrorDetails(error: Error | null, customMessage?: string): ErrorDetails {
    if (!error) {
        return {
            icon: AlertCircle,
            iconColor: "text-yellow-500",
            title: "Something went wrong",
            message: customMessage || "An unexpected error occurred. Please try again.",
            canRetry: true,
        };
    }

    const errorMessage = error.message.toLowerCase();

    // Network/Connection errors
    if (
        errorMessage.includes("network") ||
        errorMessage.includes("fetch") ||
        errorMessage.includes("failed to fetch") ||
        errorMessage.includes("timeout")
    ) {
        return {
            icon: WifiOff,
            iconColor: "text-orange-500",
            title: "Connection error",
            message: customMessage || "Unable to connect to the server. Please check your internet connection.",
            canRetry: true,
        };
    }

    // Server errors
    if (
        errorMessage.includes("500") ||
        errorMessage.includes("502") ||
        errorMessage.includes("503") ||
        errorMessage.includes("server")
    ) {
        return {
            icon: ServerCrash,
            iconColor: "text-red-500",
            title: "Server error",
            message: customMessage || "The server encountered an error. Please try again later.",
            canRetry: true,
        };
    }

    // Permission/Auth errors
    if (
        errorMessage.includes("401") ||
        errorMessage.includes("403") ||
        errorMessage.includes("unauthorized") ||
        errorMessage.includes("forbidden")
    ) {
        return {
            icon: ShieldAlert,
            iconColor: "text-purple-500",
            title: "Access denied",
            message: customMessage || "You don't have permission to access this resource.",
            canRetry: false,
        };
    }

    // Default error
    return {
        icon: AlertCircle,
        iconColor: "text-yellow-500",
        title: "Error loading data",
        message: customMessage || error.message || "Failed to load the requested data. Please try again.",
        canRetry: true,
    };
}

function QueryError({
    error,
    onRetry,
    className,
    showDetails = false,
    isRefetching = false,
    customMessage,
    size = "md",
}: QueryErrorProps) {
    const errorDetails = getErrorDetails(error, customMessage);
    const Icon = errorDetails.icon;

    const handleRetry = () => {
        if (onRetry) {
            onRetry();
        }
    };

    const isRetrying = isRefetching;
    const canRetry = errorDetails.canRetry && onRetry;

    const sizeClasses = {
        sm: {
            container: "p-4",
            icon: "w-8 h-8",
            title: "text-base",
            message: "text-sm",
            button: "text-sm px-3 py-1.5",
            details: "text-xs",
        },
        md: {
            container: "p-6",
            icon: "w-12 h-12",
            title: "text-lg",
            message: "text-base",
            button: "text-base px-4 py-2",
            details: "text-sm",
        },
        lg: {
            container: "p-8",
            icon: "w-16 h-16",
            title: "text-xl",
            message: "text-lg",
            button: "text-lg px-6 py-3",
            details: "text-base",
        },
    };

    const sizes = sizeClasses[size];

    return (
        <div className={cn("flex items-start justify-center w-full h-full min-h-[200px] pt-16", className)}>
            <div
                className={cn(
                    "flex flex-col items-center justify-center rounded-2xl",
                    "bg-gradient-to-br from-red-50 to-orange-100 dark:from-red-900/20 dark:to-orange-900/20",
                    "border border-red-200/50 dark:border-red-800/50",
                    "animate-in fade-in-0 slide-in-from-top-3 duration-500",
                    sizes.container,
                    "w-full max-w-lg"
                )}
            >
                {/* Icon */}
                <div className="flex flex-col items-center space-y-4 text-center">
                    <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                        <Icon className="w-8 h-8 text-red-600 dark:text-red-400" />
                    </div>
                </div>

                {/* Error content */}
                <div>
                    <h3 className={cn("font-semibold text-red-700 dark:text-red-300", sizes.title)}>
                        {errorDetails.title}
                    </h3>
                    <p className={cn("text-red-600 dark:text-red-400 mt-1", sizes.message)}>{errorDetails.message}</p>
                </div>

                {/* Actions */}
                {canRetry && (
                    <button
                        onClick={handleRetry}
                        disabled={isRetrying}
                        className={cn(
                            "mt-6 inline-flex items-center gap-2 rounded-lg font-medium",
                            "bg-primary/20 hover:bg-primary/30 text-primary",
                            "border border-primary/30 hover:border-primary/50",
                            "transition-all duration-200 hover:scale-105",
                            "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
                            sizes.button
                        )}
                    >
                        <RefreshCw className={cn("w-4 h-4", isRetrying && "animate-spin")} />
                        {isRetrying ? "Retrying..." : "Try Again"}
                    </button>
                )}

                {/* Error details (collapsible) */}
                {showDetails && error && (
                    <details className="mt-6 w-full max-w-md">
                        <summary
                            className={cn(
                                "cursor-pointer font-medium text-base-content/60 hover:text-base-content/80",
                                "transition-colors duration-200",
                                sizes.details
                            )}
                        >
                            Show technical details
                        </summary>
                        <div
                            className={cn(
                                "mt-3 p-3 rounded-lg bg-base-100/50 border border-base-100",
                                "text-base-content/70 font-mono break-all",
                                sizes.details
                            )}
                        >
                            <div className="space-y-2">
                                <div>
                                    <span className="font-semibold">Error:</span> {error.name}
                                </div>
                                <div>
                                    <span className="font-semibold">Message:</span> {error.message}
                                </div>
                                {error.stack && (
                                    <div>
                                        <span className="font-semibold">Stack:</span>
                                        <pre className="mt-1 overflow-x-auto text-xs opacity-70">
                                            {error.stack.split("\n").slice(0, 3).join("\n")}...
                                        </pre>
                                    </div>
                                )}
                            </div>
                        </div>
                    </details>
                )}
            </div>
        </div>
    );
}

export default QueryError;
