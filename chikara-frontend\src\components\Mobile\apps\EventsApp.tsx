import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, CheckCircle, XCircle, Star, Target, Zap, Trophy, Gift, Sparkles } from 'lucide-react';
import useGetDailyQuests from '@/features/dailytask/api/useGetDailyQuests';
import { useMissionList } from '@/features/missions/api/useMissionList';
import useGetActiveQuestList from '@/features/tasks/api/useGetActiveQuestList';
import { useCompleteDailyQuest } from '@/features/dailytask/api/useCompleteDailyQuest';

type TabType = 'daily' | 'missions' | 'quests';

const EventsApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState<TabType>('daily');
  
  // API Hooks
  const { data: dailyQuests, isLoading: loadingDaily } = useGetDailyQuests();
  const { data: missions, isLoading: loadingMissions } = useMissionList();
  const { data: activeQuests, isLoading: loadingQuests } = useGetActiveQuestList();
  const completeDailyQuest = useCompleteDailyQuest();

  const formatTimeRemaining = (hours: number) => {
    if (hours < 1) {
      return `${Math.floor(hours * 60)} minutes`;
    }
    return `${Math.floor(hours)} hours`;
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 100) return 'text-green-400';
    if (progress >= 50) return 'text-yellow-400';
    return 'text-orange-400';
  };

  const renderDailyQuests = () => {
    if (loadingDaily) {
      return (
        <div className="flex justify-center items-center py-16">
          <div className="loading loading-spinner loading-lg text-blue-400"></div>
        </div>
      );
    }

    if (!dailyQuests || dailyQuests.length === 0) {
      return (
        <div className="text-center py-16">
          <Calendar className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No daily quests available</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {dailyQuests.map((quest) => {
          const progress = quest.currentProgress && quest.targetProgress
            ? Math.min((quest.currentProgress / quest.targetProgress) * 100, 100)
            : 0;
          const isCompleted = progress >= 100;

          return (
            <motion.div
              key={quest.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-gray-800 rounded-lg p-4 border border-gray-700"
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-white font-medium">{quest.title}</h3>
                    {isCompleted && <CheckCircle className="w-4 h-4 text-green-400" />}
                  </div>
                  <p className="text-gray-400 text-sm">{quest.description}</p>
                </div>
              </div>

              {/* Progress Bar */}
              {quest.targetProgress && (
                <div className="mb-3">
                  <div className="flex justify-between text-xs text-gray-400 mb-1">
                    <span>Progress</span>
                    <span className={getProgressColor(progress)}>
                      {quest.currentProgress || 0} / {quest.targetProgress}
                    </span>
                  </div>
                  <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-gradient-to-r from-blue-500 to-blue-400"
                      initial={{ width: 0 }}
                      animate={{ width: `${progress}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>
                </div>
              )}

              {/* Rewards */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1 text-yellow-400 text-sm">
                    <Star className="w-4 h-4" />
                    <span>{quest.expReward} XP</span>
                  </div>
                  {quest.yenReward && (
                    <div className="flex items-center gap-1 text-green-400 text-sm">
                      <span>¥{quest.yenReward}</span>
                    </div>
                  )}
                </div>
                {isCompleted && !quest.completed && (
                  <button
                    onClick={() => completeDailyQuest.mutate({ questId: quest.id })}
                    disabled={completeDailyQuest.isPending}
                    className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors"
                  >
                    Claim
                  </button>
                )}
              </div>
            </motion.div>
          );
        })}
      </div>
    );
  };

  const renderMissions = () => {
    if (loadingMissions) {
      return (
        <div className="flex justify-center items-center py-16">
          <div className="loading loading-spinner loading-lg text-purple-400"></div>
        </div>
      );
    }

    if (!missions || missions.length === 0) {
      return (
        <div className="text-center py-16">
          <Target className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No missions available</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {missions.map((mission, index) => (
          <motion.div
            key={mission.id}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }}
            className={`bg-gray-800 rounded-lg p-4 border ${
              mission.status === 'in_progress' ? 'border-purple-600' : 'border-gray-700'
            }`}
          >
            <div className="flex items-start justify-between mb-2">
              <div className="flex-1">
                <h3 className="text-white font-medium mb-1">{mission.name}</h3>
                <p className="text-gray-400 text-sm mb-2">{mission.description}</p>
                
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1 text-blue-400">
                    <Clock className="w-4 h-4" />
                    <span>{formatTimeRemaining(mission.hoursToComplete)}</span>
                  </div>
                  <div className="flex items-center gap-1 text-yellow-400">
                    <Zap className="w-4 h-4" />
                    <span>{mission.energyCost} Energy</span>
                  </div>
                </div>
              </div>
              <div className={`px-2 py-1 rounded text-xs font-medium ${
                mission.status === 'in_progress' 
                  ? 'bg-purple-500/20 text-purple-400'
                  : mission.status === 'available'
                  ? 'bg-green-500/20 text-green-400'
                  : 'bg-gray-700 text-gray-400'
              }`}>
                {mission.status.replace('_', ' ').toUpperCase()}
              </div>
            </div>

            {/* Rewards */}
            <div className="mt-3 pt-3 border-t border-gray-700">
              <p className="text-xs text-gray-500 mb-1">Rewards:</p>
              <div className="flex items-center gap-3 text-sm">
                <span className="text-yellow-400">{mission.expReward} XP</span>
                <span className="text-green-400">¥{mission.yenReward}</span>
                {mission.itemRewards && mission.itemRewards.length > 0 && (
                  <span className="text-blue-400">
                    <Gift className="w-4 h-4 inline" /> +{mission.itemRewards.length}
                  </span>
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    );
  };

  const renderActiveQuests = () => {
    if (loadingQuests) {
      return (
        <div className="flex justify-center items-center py-16">
          <div className="loading loading-spinner loading-lg text-orange-400"></div>
        </div>
      );
    }

    if (!activeQuests || activeQuests.length === 0) {
      return (
        <div className="text-center py-16">
          <Trophy className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No active quests</p>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {activeQuests.map((quest, index) => (
          <motion.div
            key={quest.id}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.05 }}
            className="bg-gray-800 rounded-lg p-4 border border-gray-700"
          >
            <div className="flex items-start justify-between mb-3">
              <div>
                <h3 className="text-white font-medium flex items-center gap-2">
                  {quest.name}
                  {quest.type === 'main' && (
                    <span className="px-2 py-0.5 bg-orange-500/20 text-orange-400 text-xs rounded">
                      MAIN
                    </span>
                  )}
                </h3>
                <p className="text-gray-400 text-sm mt-1">{quest.description}</p>
              </div>
            </div>

            {/* Quest Objectives */}
            {quest.objectives && quest.objectives.length > 0 && (
              <div className="space-y-2 mb-3">
                {quest.objectives.map((objective, idx) => (
                  <div key={idx} className="flex items-center gap-2 text-sm">
                    {objective.completed ? (
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    ) : (
                      <XCircle className="w-4 h-4 text-gray-600" />
                    )}
                    <span className={objective.completed ? 'text-gray-500 line-through' : 'text-gray-300'}>
                      {objective.description}
                    </span>
                  </div>
                ))}
              </div>
            )}

            {/* Rewards */}
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1 text-yellow-400">
                <Sparkles className="w-4 h-4" />
                <span>{quest.rewards.exp} XP</span>
              </div>
              {quest.rewards.yen && (
                <div className="text-green-400">
                  ¥{quest.rewards.yen}
                </div>
              )}
              {quest.rewards.items && quest.rewards.items.length > 0 && (
                <div className="flex items-center gap-1 text-blue-400">
                  <Gift className="w-4 h-4" />
                  <span>{quest.rewards.items.length} items</span>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    );
  };

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-900 to-red-900 p-4">
        <div className="flex items-center gap-3 mb-2">
          <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
            <Calendar className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">Events & Activities</h2>
            <p className="text-orange-200 text-sm">Track your daily tasks and missions</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-gray-800 p-2 flex gap-2 border-b border-gray-700">
        {[
          { id: 'daily' as TabType, label: 'Daily', icon: Calendar },
          { id: 'missions' as TabType, label: 'Missions', icon: Target },
          { id: 'quests' as TabType, label: 'Quests', icon: Trophy },
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 py-2 px-3 rounded-lg transition-colors flex items-center justify-center gap-2 ${
              activeTab === tab.id
                ? 'bg-gray-700 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span className="text-sm">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {activeTab === 'daily' && renderDailyQuests()}
        {activeTab === 'missions' && renderMissions()}
        {activeTab === 'quests' && renderActiveQuests()}
      </div>
    </div>
  );
};

export default EventsApp;
