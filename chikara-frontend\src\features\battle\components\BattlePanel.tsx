import backButton from "@/assets/icons/UI/backButton.png";
import attackImg from "@/assets/icons/battle/attack.png";
import consumablesImg from "@/assets/icons/battle/consumables.png";
import fleeImg from "@/assets/icons/battle/flee.png";
import meleeImg from "@/assets/icons/battle/melee.png";
import rangedImg from "@/assets/icons/battle/ranged.png";
import abilitiesImg from "@/assets/icons/navitems/abilities.png";
import Image from "@/components/Image";
import clsx from "clsx";
import { Fragment, useCallback, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { usePersistStore } from "../../../app/store/stores";
import { BattleEquipment, BattlePlayer, EquippedItem, BattleAction } from "../types/battle";
import useGetEquippedAbilities from "@/features/talents/api/useGetEquippedAbilities";
import type { User } from "@/types/user";

interface EquippedConsumable {
    item: EquippedItem;
    remainingUsesInBattle: number;
}

interface EquippedConsumables {
    slot1?: EquippedConsumable;
    slot2?: EquippedConsumable;
    slot3?: EquippedConsumable;
}

// Extend the BattlePlayer interface to include equippedConsumables
interface ExtendedBattlePlayer extends BattlePlayer {
    equippedConsumables?: EquippedConsumables;
}

function countWords(inputString: string): number {
    const trimmedString = inputString.trim();
    if (trimmedString === "") {
        return 0;
    }
    const wordsArray = trimmedString.split(/\s+/);
    const numberOfWords = wordsArray.length;
    return numberOfWords;
}

interface BattlePanelProps {
    handleAction: (_action: BattleAction) => void;
    isButtonDisabled: boolean;
    player: ExtendedBattlePlayer;
    currentUser: User;
}

// Common button styles
const BUTTON_STYLES = {
    base: "relative flex size-full select-none items-center justify-center font-lili text-stroke-s-sm uppercase shadow-xs transition-all duration-200",
    enabled: "text-gray-200 hover:from-slate-600 hover:to-slate-700 hover:border-slate-500",
    disabled: "text-gray-400 grayscale opacity-50",
    darkBlue: "darkBlueButtonBGSVG",
    gradient: "bg-gradient-to-br from-slate-700 to-slate-800 border border-slate-600 rounded-lg",
};

// Keyboard shortcut configuration
const KEYBOARD_SHORTCUTS = {
    home: {
        A: "attack",
        S: "abilities",
        C: "consumables",
        R: "flee",
    },
    attack: {
        M: "melee",
        R: "ranged",
        B: "back",
    },
    abilities: {
        "1": 0,
        "2": 1,
        "3": 2,
        "4": 3,
        B: "back",
    },
};

// Base Button Component
interface BaseButtonProps {
    isDisabled?: boolean;
    onClick: () => void;
    keyboardShortcut?: string | number | null;
    className?: string;
    children: React.ReactNode;
    style?: "gradient" | "darkBlue";
}

const BaseButton = ({
    isDisabled = false,
    onClick,
    keyboardShortcut,
    className = "",
    children,
    style = "gradient",
}: BaseButtonProps) => {
    const baseClasses = clsx(
        BUTTON_STYLES.base,
        style === "gradient" ? BUTTON_STYLES.gradient : BUTTON_STYLES.darkBlue,
        isDisabled ? BUTTON_STYLES.disabled : BUTTON_STYLES.enabled,
        className
    );

    return (
        <button disabled={isDisabled} className={baseClasses} onClick={onClick}>
            {children}
            {keyboardShortcut && (
                <span className="absolute top-1 right-1 md:top-2 md:right-2 hidden text-yellow-400 text-xs md:text-sm lg:block">
                    {keyboardShortcut}
                </span>
            )}
        </button>
    );
};

// Main Battle Panel Button
interface MainButtonProps {
    text: string;
    image: string;
    imageWidth?: string;
    halfSize?: boolean;
    isDisabled?: boolean;
    keyboardShortcut?: string | null;
    onClick: () => void;
}

const MainButton = ({
    text,
    image,
    imageWidth = "w-10 md:w-14",
    halfSize = false,
    isDisabled = false,
    keyboardShortcut,
    onClick,
}: MainButtonProps) => (
    <BaseButton
        isDisabled={isDisabled}
        keyboardShortcut={keyboardShortcut}
        className={clsx("px-2 py-1 md:px-4 md:py-2 text-sm md:text-lg", halfSize ? "col-span-1" : "col-span-2")}
        onClick={onClick}
    >
        <div className="flex flex-col md:grid md:grid-cols-2 items-center gap-1 md:gap-0">
            <img className={clsx("max-h-full", imageWidth)} src={image} alt="" />
            <p className="text-center text-xs md:text-base">{text}</p>
        </div>
    </BaseButton>
);

// Weapon Button
interface WeaponButtonProps {
    text: string;
    image: string;
    subText: string;
    ammo?: number;
    isDisabled?: boolean;
    keyboardShortcut?: string | null;
    onClick: () => void;
}

const WeaponButton = ({
    text,
    image,
    subText,
    ammo,
    isDisabled = false,
    keyboardShortcut,
    onClick,
}: WeaponButtonProps) => (
    <BaseButton
        isDisabled={isDisabled}
        keyboardShortcut={keyboardShortcut}
        className="px-4 py-2 text-lg md:my-3 row-span-3"
        style="darkBlue"
        onClick={onClick}
    >
        <div className="flex flex-col items-center justify-center">
            <div className="mb-2 flex gap-x-2">
                <img className="my-auto max-h-full w-14" src={image} alt="" />
                <p className="my-auto inline md:text-xl">
                    {text} {text === "Ranged" && ammo ? ` (${ammo})` : null}
                </p>
            </div>
            {subText && <p className="text-blue-200 text-xs md:text-sm">{subText}</p>}
        </div>
    </BaseButton>
);

// Back Button
interface BackButtonProps {
    onClick: () => void;
    keyboardShortcut?: string | null;
}

const BackButton = ({ onClick, keyboardShortcut }: BackButtonProps) => (
    <BaseButton
        keyboardShortcut={keyboardShortcut}
        className="h-12 w-full px-1 text-gray-200 md:my-3"
        style="darkBlue"
        onClick={onClick}
    >
        <div className="absolute top-[45%] left-[45%] -translate-x-1/2 -translate-y-1/2 mx-auto flex gap-x-5">
            <img className="h-8 w-auto" src={backButton} alt="" />
            <p className="my-auto inline text-xl md:text-2xl">Back</p>
        </div>
    </BaseButton>
);

// Ability Button
interface AbilityButtonProps {
    ability: any;
    currentStamina: number;
    equipment: any;
    ammo: number;
    chaining: boolean;
    lastAbility: string | null;
    isDisabled?: boolean;
    keyboardShortcut?: number | string | null;
    onClick: () => void;
}

const AbilityButton = ({
    ability,
    currentStamina,
    equipment,
    ammo,
    chaining,
    lastAbility,
    isDisabled = false,
    keyboardShortcut,
    onClick,
}: AbilityButtonProps) => {
    if (!ability || ability.name === "emptyAbilitySlot") {
        return (
            <BaseButton isDisabled className="px-1 py-0 text-lg md:my-3" style="darkBlue" onClick={() => {}}>
                <div className="mb-2 size-full">
                    <div className="flex h-full">
                        <p className="m-auto inline text-base md:text-lg">Empty</p>
                    </div>
                </div>
            </BaseButton>
        );
    }

    let stamCost = ability?.staminaCost;
    if (chaining && lastAbility === ability?.name) {
        stamCost = stamCost / 2;
    }

    const isOutOfStamina = currentStamina < stamCost;
    let buttonDisabled = isOutOfStamina || isDisabled;
    let disabledMessage = "Not enough stamina!";

    if (ability.displayName === "Spray") {
        if (ammo === 0) {
            buttonDisabled = true;
            disabledMessage = "No Ammo!";
        }
        if (!equipment.ranged) {
            buttonDisabled = true;
            disabledMessage = "No Ranged weapon equipped";
        }
    }

    const handleClick = () => {
        if (buttonDisabled) {
            toast.error(disabledMessage);
        } else {
            onClick();
        }
    };

    return (
        <BaseButton
            isDisabled={isDisabled} // Visual disabled state separate from click handling
            keyboardShortcut={keyboardShortcut}
            className="text-lg md:my-1 2xl:my-3"
            style="darkBlue"
            onClick={handleClick}
        >
            <div className="flex size-full">
                <div className="mx-auto flex w-[50%] md:w-[30%]">
                    <Image className="m-auto max-h-full w-12 rounded-full md:w-14" src={ability?.image} alt="" />
                </div>
                <div className="relative flex h-full w-[70%] flex-col">
                    <div className="absolute top-1/2 left-[40%] -translate-x-1/2 -translate-y-1/2">
                        <p className={clsx(countWords(ability?.displayName) > 1 ? "" : "", "text-base md:text-xl")}>
                            {ability?.displayName}
                        </p>
                        {stamCost && (
                            <p className="my-auto text-blue-200 text-sm md:mx-auto md:text-xl">
                                <span className="text-yellow-500">{stamCost}</span>
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </BaseButton>
    );
};

export default function BattlePanel({ handleAction, isButtonDisabled, player, currentUser }: BattlePanelProps) {
    const [buttonPage, setButtonPage] = useState("home");
    const [lastAbility, setLastAbility] = useState<string | null>(null);

    const { data: equippedAbilitiesData = [] } = useGetEquippedAbilities();
    const { keyboardShortcutsEnabled } = usePersistStore();

    const equipment = player.equipment as BattleEquipment;
    const equippedRangedWeapon = equipment?.ranged;
    const equippedMeleeWeapon = equipment?.weapon;
    const equippedConsumables = player.equippedConsumables;
    const statusEffects = player.statusEffects;
    const stamina = currentUser?.currentStamina;

    const hasConsumables =
        equippedConsumables && (equippedConsumables.slot1 || equippedConsumables.slot2 || equippedConsumables.slot3);

    const abilityLocked = statusEffects["ability_lock"] || statusEffects["ability_lock_debuff"];
    const chaining = statusEffects["lastCastAmount"] === 2;
    const areAllEmpty = equippedAbilitiesData.every((ability) => !ability);

    const handleAttack = useCallback(
        (attackType: BattleAction) => {
            setButtonPage("home");
            if (attackType !== "attack" && attackType !== "ranged") {
                setLastAbility(attackType);
            }
            handleAction(attackType);
        },
        [handleAction]
    );

    // Simplified keyboard shortcut handling
    useEffect(() => {
        if (!keyboardShortcutsEnabled || isButtonDisabled) return;

        const handleKeyDown = (event: KeyboardEvent) => {
            const activeElement = document.activeElement;
            const isInputField =
                activeElement && (activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA");
            if (isInputField) return;

            const shortcuts = KEYBOARD_SHORTCUTS[buttonPage as keyof typeof KEYBOARD_SHORTCUTS];
            if (!shortcuts) return;

            const key = event.key.toUpperCase();
            const action = shortcuts[key as keyof typeof shortcuts];

            if (!action) return;

            // Handle page navigation
            if (action === "back") {
                setButtonPage("home");
                return;
            }

            // Handle main page actions
            if (buttonPage === "home") {
                if (action === "attack") setButtonPage("attack");
                else if (action === "abilities") setButtonPage("abilities");
                else if (action === "consumables") setButtonPage("consumables");
                else if (action === "flee") handleAction("flee");
                return;
            }

            // Handle attack page actions
            if (buttonPage === "attack") {
                if (action === "melee") handleAttack("attack");
                else if (action === "ranged" && equippedRangedWeapon && statusEffects?.ammo >= 1) {
                    handleAttack("ranged");
                }
                return;
            }

            // Handle abilities page actions
            if (buttonPage === "abilities" && typeof action === "number") {
                const ability = equippedAbilitiesData[action];
                if (ability?.name) handleAttack(ability.name);
                return;
            }
        };

        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [
        keyboardShortcutsEnabled,
        isButtonDisabled,
        buttonPage,
        handleAttack,
        handleAction,
        equippedAbilitiesData,
        equippedRangedWeapon,
        statusEffects?.ammo,
        equippedConsumables,
    ]);

    // Render different pages
    if (buttonPage === "attack") {
        const rangedSubText = !equippedRangedWeapon ? "No Ranged weapon" : `Weapon: ${equippedRangedWeapon.name}`;

        const meleeSubText = !equippedMeleeWeapon ? "Weapon: Fists" : `Weapon: ${equippedMeleeWeapon.name}`;

        return (
            <div className="flex flex-col gap-2">
                <div className="grid h-32 grid-cols-2 gap-2 md:h-auto 2xl:h-32">
                    <WeaponButton
                        text="Melee"
                        image={meleeImg}
                        subText={meleeSubText}
                        keyboardShortcut={keyboardShortcutsEnabled ? "M" : null}
                        onClick={() => handleAttack("attack")}
                    />
                    <WeaponButton
                        text="Ranged"
                        image={rangedImg}
                        subText={rangedSubText}
                        ammo={statusEffects?.ammo}
                        keyboardShortcut={keyboardShortcutsEnabled ? "R" : null}
                        isDisabled={!currentUser?.equippedRangedWeaponId || statusEffects?.ammo < 1}
                        onClick={() => handleAttack("ranged")}
                    />
                </div>
                <BackButton
                    keyboardShortcut={keyboardShortcutsEnabled ? "B" : null}
                    onClick={() => setButtonPage("home")}
                />
            </div>
        );
    }

    if (buttonPage === "abilities") {
        return (
            <div className="flex flex-col gap-2">
                <div className="grid h-48 grid-cols-2 grid-rows-2 gap-2">
                    {[0, 1, 2, 3].map((i) => (
                        <Fragment key={i}>
                            <AbilityButton
                                ability={equippedAbilitiesData[i]}
                                currentStamina={stamina}
                                equipment={equipment}
                                ammo={statusEffects?.ammo}
                                chaining={chaining}
                                lastAbility={lastAbility}
                                isDisabled={isButtonDisabled}
                                keyboardShortcut={keyboardShortcutsEnabled ? i + 1 : null}
                                onClick={() =>
                                    equippedAbilitiesData[i]?.name && handleAttack(equippedAbilitiesData[i].name)
                                }
                            />
                        </Fragment>
                    ))}
                </div>
                <BackButton
                    keyboardShortcut={keyboardShortcutsEnabled ? "B" : null}
                    onClick={() => setButtonPage("home")}
                />
            </div>
        );
    }

    if (buttonPage === "consumables") {
        return (
            <div className="flex flex-col gap-2">
                <BackButton
                    keyboardShortcut={keyboardShortcutsEnabled ? "B" : null}
                    onClick={() => setButtonPage("home")}
                />
            </div>
        );
    }

    // Home page
    return (
        <div className="grid h-32 md:h-48 grid-cols-4 grid-rows-2 gap-1 md:gap-2">
            <MainButton
                text="Attack"
                image={attackImg}
                isDisabled={isButtonDisabled}
                keyboardShortcut={keyboardShortcutsEnabled ? "A" : null}
                onClick={() => setButtonPage("attack")}
            />
            <MainButton
                text="Abilities"
                image={abilitiesImg}
                imageWidth="w-8 md:w-11"
                isDisabled={isButtonDisabled || areAllEmpty || abilityLocked}
                keyboardShortcut={keyboardShortcutsEnabled ? "S" : null}
                onClick={() => setButtonPage("abilities")}
            />
            <MainButton
                text="Items"
                image={consumablesImg}
                halfSize={false}
                isDisabled={isButtonDisabled || !hasConsumables}
                keyboardShortcut={keyboardShortcutsEnabled ? "C" : null}
                onClick={() => setButtonPage("consumables")}
            />
            <MainButton
                text="Run"
                image={fleeImg}
                isDisabled={isButtonDisabled}
                keyboardShortcut={keyboardShortcutsEnabled ? "R" : null}
                onClick={() => handleAction("flee")}
            />
        </div>
    );
}
