import React from "react";
import { cn } from "@/lib/utils";
import type { UseQueryResult } from "@tanstack/react-query";
import QueryLoading from "./QueryLoading";
import QueryError from "./QueryError";

interface QueryWrapperProps {
    /**
     * The query result from TanStack Query
     */
    queryResult: UseQueryResult<any, Error>;
    /**
     * Content to render when the query is successful
     */
    children: React.ReactNode;
    /**
     * Additional className for the wrapper container
     */
    className?: string;
    /**
     * Props to pass to QueryLoading component
     */
    loadingProps?: {
        className?: string;
        showSkeleton?: boolean;
        size?: "sm" | "md" | "lg";
    };
    /**
     * Props to pass to QueryError component
     */
    errorProps?: {
        className?: string;
        showDetails?: boolean;
        customMessage?: string;
        size?: "sm" | "md" | "lg";
        onRetry?: () => void;
    };
    /**
     * Whether to show loading state during refetching
     */
    showRefetchingLoading?: boolean;
}

/**
 * QueryWrapper - A component that automatically handles loading and error states for TanStack Query
 *
 * @example
 * ```tsx
 * const query = useGetData();
 *
 * return (
 *   <QueryWrapper
 *     queryResult={query}
 *     errorProps={{ customMessage: "Failed to load data" }}
 *   >
 *     <DataDisplay data={query.data} />
 *   </QueryWrapper>
 * );
 * ```
 */
function QueryWrapper({
    queryResult,
    children,
    className,
    loadingProps = {},
    errorProps = {},
    showRefetchingLoading = false,
}: QueryWrapperProps) {
    const { isLoading, error, isRefetching } = queryResult;

    // Show loading state
    if (isLoading || (showRefetchingLoading && isRefetching)) {
        return (
            <div className={className}>
                <QueryLoading isLoading={isLoading || (showRefetchingLoading && isRefetching)} {...loadingProps} />
            </div>
        );
    }

    // Show error state
    if (error) {
        return (
            <div className={className}>
                <QueryError
                    error={error}
                    isRefetching={isRefetching}
                    onRetry={() => queryResult.refetch()}
                    {...errorProps}
                />
            </div>
        );
    }

    // Show children when successful
    return <>{children}</>;
}

export default QueryWrapper;
