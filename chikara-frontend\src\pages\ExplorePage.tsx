import { usePersistStore } from "@/app/store/stores";
import { ExploreLocationsView, ExploreView } from "@/features/explore";
import { TravelScreen } from "@/features/explore/components/TravelScreen";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { ArrowLeft, List, Map, MapPin, Navigation } from "lucide-react";
import { sceneManager } from "@/helpers/sceneManager";
import PageHeader from "@/components/Layout/PageHeader";
import useExploreMap from "@/features/explore/api/useExploreMap";
import { useMemo } from "react";

const DISABLE_EXPLORE_LIST_VIEW = true;

export default function ExplorePage() {
    const { data: currentUser } = useFetchCurrentUser();
    const { explorePageSetting, setExplorePageSetting } = usePersistStore();
    const { data: mapData, isLoading, error } = useExploreMap();

    const districtImage = sceneManager("highstreet2");
    const districtName = capitaliseFirstLetter(currentUser?.currentMapLocation || "Unknown");

    // Extract travel status and nodes from map data
    const travelStatus = mapData?.travelStatus;
    const nodes = mapData?.nodes || [];

    // Check if user is currently traveling
    const isTravel = travelStatus?.isTravel || false;

    // Determine if the user is currently inside an encounter (story, character, scavenging, mining, foraging)
    const isEncounter = useMemo(() => {
        if (!nodes.length) return false;
        const current = nodes.find((n) => n.status === "current");
        if (!current) return false;
        return ["CHARACTER_ENCOUNTER", "STORY", "SCAVENGE_NODE", "MINING_NODE", "FORAGING_NODE"].includes(
            current.nodeType as string
        );
    }, [mapData]);

    // Dynamic title based on current page setting
    const getTitle = () => {
        if (explorePageSetting === "travel") {
            return (
                <div className="relative">
                    {/* Glow background layers */}
                    <div className="absolute inset-0 blur-3xl opacity-60">
                        <h1 className="text-3xl sm:text-5xl lg:text-6xl font-black text-purple-400 tracking-tight">
                            Explore Tokyo
                        </h1>
                    </div>
                    <div className="absolute inset-0 blur-xl opacity-40">
                        <h1 className="text-3xl sm:text-5xl lg:text-6xl font-black text-pink-300 tracking-tight">
                            Explore Tokyo
                        </h1>
                    </div>
                    {/* Main title */}
                    <h1
                        className="relative text-3xl sm:text-5xl lg:text-6xl font-black text-white tracking-tight"
                        style={{
                            textShadow: `
                                0 0 20px rgba(168, 85, 247, 0.8),
                                0 0 40px rgba(236, 72, 153, 0.6),
                                0 0 60px rgba(34, 211, 238, 0.4),
                                2px 2px 4px rgba(0, 0, 0, 0.8)
                            `,
                        }}
                    >
                        Explore Tokyo
                    </h1>
                </div>
            );
        }
        return districtName;
    };

    // Dynamic subtitle
    const getSubtitle = () => {
        return explorePageSetting === "travel"
            ? "Choose your next destination"
            : "Discover what this district has to offer";
    };

    // Additional info for travel mode
    const getAdditionalInfo = () => {
        if (explorePageSetting === "travel") {
            return (
                <div className="flex items-center space-x-2 sm:space-x-3 mt-3">
                    <MapPin className="w-4 h-4 sm:w-5 sm:h-5 text-purple-300 animate-bounce" />
                    <span className="text-sm sm:text-base text-purple-200 font-semibold">
                        <span className="inline">Currently in: </span>
                        <span className="text-white font-bold bg-purple-600/20 px-2 sm:px-3 py-1 rounded-full backdrop-blur-sm border border-purple-400/30">
                            {districtName}
                        </span>
                    </span>
                </div>
            );
        }
        return null;
    };

    // Action button
    const getActionButton = () => {
        // Hide all action buttons when in encounter
        if (isEncounter) {
            return null;
        }

        return (
            <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl blur-lg opacity-60 animate-pulse" />
                <div className="relative flex rounded-2xl bg-gray-900/80 backdrop-blur-md shadow-2xl border border-white/20 hover:border-white/40 transition-all duration-300">
                    {explorePageSetting === "travel" ? (
                        <button
                            className="flex items-center space-x-2 sm:space-x-3 px-4 sm:px-6 py-3 sm:py-4 text-sm font-bold text-white hover:bg-white/10 transition-all duration-300 rounded-2xl group"
                            onClick={() => setExplorePageSetting("map")}
                        >
                            <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 group-hover:-translate-x-1 transition-transform" />
                            <span className="hidden xs:inline sm:inline">Back</span>
                        </button>
                    ) : (
                        <button
                            disabled={isTravel}
                            className={cn(
                                "flex items-center space-x-2 sm:space-x-3 px-4 sm:px-6 py-3 sm:py-4 text-sm font-bold transition-all duration-300 rounded-2xl group",
                                isTravel
                                    ? "text-gray-400 cursor-not-allowed opacity-60"
                                    : "text-white hover:bg-white/10"
                            )}
                            onClick={() => !isTravel && setExplorePageSetting("travel")}
                        >
                            <Navigation
                                className={cn(
                                    "w-4 h-4 sm:w-5 sm:h-5 transition-transform",
                                    !isTravel && "group-hover:rotate-12"
                                )}
                            />
                            <span className="inline">{isTravel ? "Traveling..." : "Travel"}</span>
                        </button>
                    )}
                </div>
            </div>
        );
    };

    // Custom title theme for travel mode
    const getTitleTheme = () => {
        if (explorePageSetting === "travel") {
            return {
                primary: "rgba(168, 85, 247, 0.8)", // purple
                secondary: "rgba(236, 72, 153, 0.6)", // pink
                tertiary: "rgba(34, 211, 238, 0.4)", // cyan
                textShadow: `
                    0 0 20px rgba(168, 85, 247, 0.8),
                    0 0 40px rgba(236, 72, 153, 0.6),
                    0 0 60px rgba(34, 211, 238, 0.4),
                    2px 2px 4px rgba(0, 0, 0, 0.8)
                `,
            };
        }
        return undefined; // Use default theme for district view
    };

    return (
        <div className="-m-2 md:-m-4">
            {/* Enhanced Dynamic Header */}
            <PageHeader
                backgroundImage={districtImage || ""}
                title={getTitle()}
                subtitle={getSubtitle()}
                additionalInfo={getAdditionalInfo()}
                actionButton={getActionButton()}
                titleTheme={getTitleTheme()}
                useNegativeZIndex={false}
            />

            {/* Main Content */}
            <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 -mt-10">
                {!isEncounter && explorePageSetting !== "travel" && !DISABLE_EXPLORE_LIST_VIEW && (
                    <div className="flex items-center bg-white/90 dark:bg-gray-800/90 backdrop-blur-md rounded-2xl w-fit mb-6 shadow-xl border border-gray-200/50 dark:border-gray-700/50">
                        <button
                            disabled={isTravel}
                            className={cn(
                                "px-5 py-2.5 rounded-xl transition-all duration-300 flex items-center gap-3 text-sm font-semibold",
                                isTravel && "opacity-50 cursor-not-allowed",
                                explorePageSetting === "map"
                                    ? "bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg shadow-purple-500/25 scale-105"
                                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700/50"
                            )}
                            onClick={() => !isTravel && setExplorePageSetting("map")}
                        >
                            <Map className="w-4 h-4" />
                            <span>Map View</span>
                        </button>
                        <button
                            disabled={isTravel}
                            className={cn(
                                "px-5 py-2.5 rounded-xl transition-all duration-300 flex items-center gap-3 text-sm font-semibold",
                                isTravel && "opacity-50 cursor-not-allowed",
                                explorePageSetting === "list"
                                    ? "bg-gradient-to-r from-purple-600 to-purple-700 text-white shadow-lg shadow-purple-500/25 scale-105"
                                    : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-700/50"
                            )}
                            onClick={() => !isTravel && setExplorePageSetting("list")}
                        >
                            <List className="w-4 h-4" />
                            <span>List View</span>
                        </button>
                    </div>
                )}
                {isTravel && travelStatus ? (
                    <div className="flex items-center justify-center min-h-[400px] lg:min-h-[600px] w-full">
                        <TravelScreen travelStatus={travelStatus} />
                    </div>
                ) : (
                    <>
                        {explorePageSetting === "travel" ? (
                            <ExploreLocationsView className="w-full" travelStatus={travelStatus} />
                        ) : (
                            <ExploreView
                                mapData={nodes}
                                isLoading={isLoading}
                                error={error}
                                viewType={DISABLE_EXPLORE_LIST_VIEW ? "map" : explorePageSetting}
                                className="w-full"
                                currentView={currentUser?.currentMapLocation}
                            />
                        )}
                    </>
                )}
            </div>
        </div>
    );
}
