import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { DollarSign, TrendingUp, TrendingDown, Send, ArrowUpRight, ArrowDownLeft, CreditCard, Users, GraduationCap } from 'lucide-react';

const BankApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'transfer'>('overview');
  
  const accountData = {
    balance: 125750,
    gangCredits: 3420,
    classroomPoints: 850,
    monthlyChange: 12.5,
  };

  const transactions = [
    { id: '1', type: 'income', description: 'Mission Reward', amount: 5000, time: '2 hours ago' },
    { id: '2', type: 'expense', description: 'Equipment Purchase', amount: -2500, time: '5 hours ago' },
    { id: '3', type: 'income', description: 'Gang Territory Bonus', amount: 1200, time: '1 day ago' },
    { id: '4', type: 'expense', description: 'Training Fee', amount: -800, time: '2 days ago' },
    { id: '5', type: 'income', description: 'PvP Victory', amount: 3000, time: '3 days ago' },
  ];

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`;
  };

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* Account Summary */}
      <div className="bg-gradient-to-r from-blue-900 to-purple-900 p-6">
        <div className="mb-4">
          <p className="text-blue-200 text-sm">Total Balance</p>
          <h2 className="text-3xl font-bold text-white">{formatCurrency(accountData.balance)}</h2>
          <div className="flex items-center gap-2 mt-2">
            <TrendingUp className="w-4 h-4 text-green-400" />
            <span className="text-green-400 text-sm">+{accountData.monthlyChange}% this month</span>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                <Users className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-xs text-blue-200">Gang Credits</p>
                <p className="text-white font-semibold">{accountData.gangCredits}</p>
              </div>
            </div>
          </div>
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-3">
            <div className="flex items-center gap-2 mb-1">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <GraduationCap className="w-4 h-4 text-white" />
              </div>
              <div>
                <p className="text-xs text-blue-200">Class Points</p>
                <p className="text-white font-semibold">{accountData.classroomPoints}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-gray-800 p-2 flex gap-2 border-b border-gray-700">
        {['overview', 'transactions', 'transfer'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab as any)}
            className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
              activeTab === tab
                ? 'bg-gray-700 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'overview' && (
          <div className="p-4 space-y-4">
            {/* Quick Actions */}
            <div className="grid grid-cols-2 gap-3">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gray-800 rounded-lg p-4 flex flex-col items-center gap-2 hover:bg-gray-700 transition-colors"
              >
                <Send className="w-6 h-6 text-blue-400" />
                <span className="text-white text-sm">Send Money</span>
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gray-800 rounded-lg p-4 flex flex-col items-center gap-2 hover:bg-gray-700 transition-colors"
              >
                <CreditCard className="w-6 h-6 text-purple-400" />
                <span className="text-white text-sm">Cards</span>
              </motion.button>
            </div>

            {/* Recent Activity */}
            <div>
              <h3 className="text-white font-medium mb-3">Recent Activity</h3>
              <div className="space-y-2">
                {transactions.slice(0, 3).map((transaction) => (
                  <div key={transaction.id} className="bg-gray-800 rounded-lg p-3 flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        transaction.type === 'income' ? 'bg-green-500/20' : 'bg-red-500/20'
                      }`}>
                        {transaction.type === 'income' ? (
                          <ArrowDownLeft className="w-5 h-5 text-green-400" />
                        ) : (
                          <ArrowUpRight className="w-5 h-5 text-red-400" />
                        )}
                      </div>
                      <div>
                        <p className="text-white text-sm">{transaction.description}</p>
                        <p className="text-gray-400 text-xs">{transaction.time}</p>
                      </div>
                    </div>
                    <p className={`font-semibold ${
                      transaction.type === 'income' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {transaction.type === 'income' ? '+' : ''}{formatCurrency(transaction.amount)}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="p-4">
            <div className="space-y-2">
              {transactions.map((transaction, index) => (
                <motion.div
                  key={transaction.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="bg-gray-800 rounded-lg p-3 flex items-center justify-between"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      transaction.type === 'income' ? 'bg-green-500/20' : 'bg-red-500/20'
                    }`}>
                      {transaction.type === 'income' ? (
                        <ArrowDownLeft className="w-5 h-5 text-green-400" />
                      ) : (
                        <ArrowUpRight className="w-5 h-5 text-red-400" />
                      )}
                    </div>
                    <div>
                      <p className="text-white text-sm">{transaction.description}</p>
                      <p className="text-gray-400 text-xs">{transaction.time}</p>
                    </div>
                  </div>
                  <p className={`font-semibold ${
                    transaction.type === 'income' ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {transaction.type === 'income' ? '+' : ''}{formatCurrency(transaction.amount)}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'transfer' && (
          <div className="p-4">
            <form className="space-y-4">
              <div>
                <label className="text-gray-400 text-sm mb-2 block">Recipient</label>
                <input
                  type="text"
                  placeholder="Enter username or ID"
                  className="w-full bg-gray-800 text-white px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="text-gray-400 text-sm mb-2 block">Amount</label>
                <input
                  type="number"
                  placeholder="0"
                  className="w-full bg-gray-800 text-white px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="text-gray-400 text-sm mb-2 block">Currency</label>
                <select className="w-full bg-gray-800 text-white px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>Yen (¥)</option>
                  <option>Gang Credits</option>
                  <option>Classroom Points</option>
                </select>
              </div>
              <div>
                <label className="text-gray-400 text-sm mb-2 block">Note (optional)</label>
                <textarea
                  placeholder="Add a note..."
                  rows={3}
                  className="w-full bg-gray-800 text-white px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-lg font-medium transition-colors"
              >
                Send Money
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default BankApp;
