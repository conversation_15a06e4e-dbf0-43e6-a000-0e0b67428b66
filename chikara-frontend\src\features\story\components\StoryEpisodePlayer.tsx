import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { MessageSquare, <PERSON>rk<PERSON>, ChevronRight, ChevronLeft, ArrowLeft } from "lucide-react";
import { useCompleteEpisode } from "../api/useCompleteEpisode";
import { StoryContent, StoryEpisodeData } from "../types/story";
import { ExploreViewModal } from "../../explore/components/ExploreViewModal";


export const StoryEpisodePlayer: React.FC<{ episodeData: StoryEpisodeData; onClose: () => void }> = ({
    episodeData,
    onClose,
}) => {
    const navigate = useNavigate();
    const completeEpisodeMutation = useCompleteEpisode();
    const [showText, setShowText] = useState(false);
    const [currentSceneIndex, setCurrentSceneIndex] = useState(0);

    const episode = episodeData;
    const content = episode?.content as StoryContent;
    const scenes = content?.scenes || [];
    const currentScene = scenes[currentSceneIndex];

// Animate text appearance
    useEffect(() => {
        setShowText(false);
        const timer = setTimeout(() => setShowText(true), 100);
        return () => clearTimeout(timer);
    }, [currentSceneIndex]);


    const handleCompleteEpisode = async () => {
        try {
            if (!episode?.id) {
                console.error("No episode ID available");
                return;
            }

            await completeEpisodeMutation.mutateAsync({
                episodeId: episode.id,
                choices: {},
            });

            // Close the episode player - this will trigger explore map refresh
            // and quest progress updates
            onClose();
        } catch (error) {
            console.error("Failed to complete episode:", error);
        }
    };

    const handleNextScene = () => {
        if (currentSceneIndex < scenes.length - 1) {
            setCurrentSceneIndex(currentSceneIndex + 1);
        } else {
            // Episode completed
            handleCompleteEpisode();
        }
    };

    const handlePreviousScene = () => {
        if (currentSceneIndex > 0) {
            setCurrentSceneIndex(currentSceneIndex - 1);
        }
    };

    const handleSkipToEnd = () => {
        setCurrentSceneIndex(scenes.length - 1);
    };

    if (!episode || !content) {
        return (
            <div className="flex items-center justify-center h-screen bg-gray-900">
                <div className="text-center">
                    <h2 className="text-2xl font-bold mb-4">Episode Not Found</h2>
                    <button 
                        className="btn btn-primary"
                        onClick={() => navigate("/explore")}
                    >
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Explore
                    </button>
                </div>
            </div>
        );
    }

    const progressPercentage = ((currentSceneIndex + 1) / scenes.length) * 100;

    return (
        <ExploreViewModal
            title="Story Episode"
            subtitle={episode.episodeType}
            borderColor="border-amber-500/80"
            headerBorderColor="border-amber-500/30"
            titleColor="text-amber-400"
            showWipeAnimation
        >
            {/* Character Avatar and Speaker */}
            {currentScene?.speaker && (
                <div className="mb-6 text-center animate-fade-in">
                    <div className="avatar placeholder mb-2">
                        <div className="bg-gradient-to-br from-amber-500 to-yellow-600 text-amber-100 rounded-full w-20 h-20 md:w-24 md:h-24 ring-4 ring-amber-400/50">
                            <span className="text-2xl md:text-3xl font-bold">
                                {currentScene.speaker.charAt(0)}
                            </span>
                        </div>
                    </div>
                    <h3 className="text-lg md:text-xl font-bold text-amber-400">
                        {currentScene.speaker}
                    </h3>
                    {currentScene.character && (
                        <p className="text-sm text-gray-400">{currentScene.character}</p>
                    )}
                </div>
            )}
            
            {/* Scene Counter and Progress */}
            <div className="flex justify-between items-center mb-4">
                <div className="badge badge-lg bg-amber-500/20 text-amber-400 border-amber-400/30">
                    Scene {currentSceneIndex + 1} of {scenes.length}
                </div>
                <div className="flex items-center gap-2">
                    {/* Progress Indicator */}
                    <div className="radial-progress text-amber-400" style={{"--value": progressPercentage} as React.CSSProperties} role="progressbar">
                        <span className="text-xs">{Math.round(progressPercentage)}%</span>
                    </div>
                    {/* Skip Button */}
                    <button
                        className="btn btn-circle btn-ghost btn-sm"
                        onClick={handleSkipToEnd}
                        title="Skip to end"
                    >
                        <Sparkles className="h-4 w-4" />
                    </button>
                </div>
            </div>

            {/* Dialogue Text */}
            <div className="flex-1 flex flex-col justify-center">
                <div className={`min-h-[120px] mb-6 transition-opacity duration-300 ${
                    showText ? 'opacity-100' : 'opacity-0'
                }`}>
                    <p className="text-base md:text-lg lg:text-xl leading-relaxed text-gray-200">
                        {currentScene?.text}
                    </p>
                </div>

                {/* Scene Effects */}
                {currentScene?.effects && currentScene.effects.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-4">
                        {currentScene.effects.map((effect, index) => (
                            <div key={index} className="badge badge-lg bg-amber-500/20 text-amber-400 border-amber-400/30">
                                {effect}
                            </div>
                        ))}
                    </div>
                )}
            </div>

            {/* Navigation Controls */}
            {currentScene?.type !== "choice" && (
                <div className="flex justify-between items-center gap-4">
                    <button
                        className="btn btn-ghost btn-sm text-gray-300"
                        disabled={currentSceneIndex === 0}
                        onClick={handlePreviousScene}
                    >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                    </button>

                    <div className="flex-1">
                        <progress 
                            className="progress progress-primary w-full" 
                            value={progressPercentage} 
                            max="100"
                        />
                    </div>

                    <button
                        className={`btn btn-sm ${
                            currentSceneIndex === scenes.length - 1 ? 'btn-warning' : 'btn-primary'
                        }`}
                        disabled={completeEpisodeMutation.isPending}
                        onClick={handleNextScene}
                    >
                        {completeEpisodeMutation.isPending ? (
                            <>
                                <span className="loading loading-spinner loading-xs"></span>
                                Completing...
                            </>
                        ) : (
                            <>
                                {currentSceneIndex === scenes.length - 1 ? 'Complete' : 'Continue'}
                                <ChevronRight className="h-4 w-4 ml-1" />
                            </>
                        )}
                    </button>
                </div>
            )}
        </ExploreViewModal>
    );
};
