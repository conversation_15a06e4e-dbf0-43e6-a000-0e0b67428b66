import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Phone, PhoneMissed, PhoneIncoming, PhoneOutgoing, User } from 'lucide-react';
import { MobileContact } from '../types';

const mockContacts: MobileContact[] = [
  { id: '1', name: '<PERSON> Leader', status: 'online' },
  { id: '2', name: '<PERSON><PERSON> Takeda', status: 'offline', lastSeen: '2 hours ago' },
  { id: '3', name: 'Shop Owner', status: 'busy' },
  { id: '4', name: 'Rival Gang Member', status: 'offline', lastSeen: 'Yesterday' },
];

const recentCalls = [
  { id: '1', name: '<PERSON> Leader', type: 'incoming', time: '10:45 AM', duration: '5:23' },
  { id: '2', name: 'Unknown', type: 'missed', time: '9:30 AM' },
  { id: '3', name: '<PERSON><PERSON> Takeda', type: 'outgoing', time: 'Yesterday', duration: '12:45' },
];

const PhoneApp: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'keypad' | 'recent' | 'contacts'>('keypad');
  const [phoneNumber, setPhoneNumber] = useState('');

  const handleKeyPress = (key: string) => {
    if (key === 'delete') {
      setPhoneNumber(phoneNumber.slice(0, -1));
    } else {
      setPhoneNumber(phoneNumber + key);
    }
  };

  const handleCall = () => {
    if (phoneNumber) {
      // Handle making call
      console.log('Calling:', phoneNumber);
    }
  };

  const renderCallIcon = (type: string) => {
    switch (type) {
      case 'incoming':
        return <PhoneIncoming className="w-4 h-4 text-green-500" />;
      case 'missed':
        return <PhoneMissed className="w-4 h-4 text-red-500" />;
      case 'outgoing':
        return <PhoneOutgoing className="w-4 h-4 text-blue-500" />;
      default:
        return <Phone className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-900">
      {/* Tabs */}
      <div className="bg-gray-800 p-2 flex gap-2 border-b border-gray-700">
        {['keypad', 'recent', 'contacts'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab as any)}
            className={`flex-1 py-2 px-4 rounded-lg transition-colors ${
              activeTab === tab
                ? 'bg-gray-700 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'keypad' && (
          <div className="h-full flex flex-col justify-between p-4">
            {/* Number Display */}
            <div className="text-center py-8">
              <div className="text-3xl text-white font-light min-h-[40px]">
                {phoneNumber || 'Enter number'}
              </div>
            </div>

            {/* Keypad */}
            <div className="grid grid-cols-3 gap-3 mb-4">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, '*', 0, '#'].map((key) => (
                <motion.button
                  key={key}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleKeyPress(key.toString())}
                  className="h-16 bg-gray-800 hover:bg-gray-700 rounded-full flex flex-col items-center justify-center transition-colors"
                >
                  <span className="text-2xl text-white">{key}</span>
                  {key === 2 && <span className="text-xs text-gray-400">ABC</span>}
                  {key === 3 && <span className="text-xs text-gray-400">DEF</span>}
                  {key === 4 && <span className="text-xs text-gray-400">GHI</span>}
                  {key === 5 && <span className="text-xs text-gray-400">JKL</span>}
                  {key === 6 && <span className="text-xs text-gray-400">MNO</span>}
                  {key === 7 && <span className="text-xs text-gray-400">PQRS</span>}
                  {key === 8 && <span className="text-xs text-gray-400">TUV</span>}
                  {key === 9 && <span className="text-xs text-gray-400">WXYZ</span>}
                </motion.button>
              ))}
            </div>

            {/* Call Button */}
            <div className="flex justify-center gap-4">
              <motion.button
                whileTap={{ scale: 0.95 }}
                onClick={handleCall}
                className="w-20 h-20 bg-green-500 hover:bg-green-600 rounded-full flex items-center justify-center transition-colors"
              >
                <Phone className="w-8 h-8 text-white" />
              </motion.button>
              {phoneNumber && (
                <motion.button
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleKeyPress('delete')}
                  className="w-20 h-20 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center transition-colors"
                >
                  <span className="text-white text-2xl">⌫</span>
                </motion.button>
              )}
            </div>
          </div>
        )}

        {activeTab === 'recent' && (
          <div className="overflow-y-auto">
            {recentCalls.map((call, index) => (
              <motion.div
                key={call.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                className="p-4 flex items-center gap-3 hover:bg-gray-800 transition-colors border-b border-gray-800"
              >
                <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-gray-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium">{call.name}</h3>
                  <div className="flex items-center gap-2 text-sm text-gray-400">
                    {renderCallIcon(call.type)}
                    <span>{call.time}</span>
                    {call.duration && <span>• {call.duration}</span>}
                  </div>
                </div>
                <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                  <Phone className="w-5 h-5 text-white" />
                </button>
              </motion.div>
            ))}
          </div>
        )}

        {activeTab === 'contacts' && (
          <div className="overflow-y-auto">
            {mockContacts.map((contact, index) => (
              <motion.div
                key={contact.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                className="p-4 flex items-center gap-3 hover:bg-gray-800 transition-colors border-b border-gray-800"
              >
                <div className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-gray-400" />
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium">{contact.name}</h3>
                  <p className="text-sm text-gray-400">
                    {contact.status === 'online' && 'Active now'}
                    {contact.status === 'busy' && 'Busy'}
                    {contact.status === 'offline' && contact.lastSeen}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    contact.status === 'online' ? 'bg-green-500' :
                    contact.status === 'busy' ? 'bg-yellow-500' :
                    'bg-gray-500'
                  }`} />
                  <button className="p-2 hover:bg-gray-700 rounded-lg transition-colors">
                    <Phone className="w-5 h-5 text-white" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PhoneApp;
