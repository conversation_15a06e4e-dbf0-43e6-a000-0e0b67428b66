export type MobileAppType = 
  | 'messages'
  | 'phone'
  | 'gang'
  | 'map'
  | 'camera'
  | 'calendar'
  | 'bank'
  | 'shop'
  | 'news'
  | 'arcade'
  | 'rankings'
  | 'property'
  | 'combat'
  | 'academy'
  | 'music'
  | 'settings';

export interface MobileMessage {
  id: string;
  sender: string;
  avatar?: string;
  message: string;
  time: string;
  unread?: boolean;
}

export interface MobileContact {
  id: string;
  name: string;
  avatar?: string;
  status?: 'online' | 'offline' | 'busy';
  lastSeen?: string;
}
