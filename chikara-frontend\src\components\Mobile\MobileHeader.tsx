import React from 'react';
import { Wifi, Battery, Signal } from 'lucide-react';

interface MobileHeaderProps {
  time: Date;
}

const MobileHeader: React.FC<MobileHeaderProps> = ({ time }) => {
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  return (
    <div className="absolute top-0 left-0 right-0 h-12 bg-gray-950/90 backdrop-blur-md flex items-center justify-between px-6 z-10">
      {/* Left Side - Time */}
      <div className="text-white text-sm font-medium">
        {formatTime(time)}
      </div>

      {/* Center - Notch Space */}
      <div className="w-32" />

      {/* Right Side - Status Icons */}
      <div className="flex items-center gap-1">
        <Signal className="w-4 h-4 text-white" />
        <Wifi className="w-4 h-4 text-white" />
        <Battery className="w-4 h-4 text-white" />
        <span className="text-white text-xs ml-1">87%</span>
      </div>
    </div>
  );
};

export default MobileHeader;
