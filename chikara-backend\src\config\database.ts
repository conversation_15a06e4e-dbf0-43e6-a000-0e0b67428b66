import { PrismaMariaDb } from "@prisma/adapter-mariadb";
import { LogErrorStack } from "../utils/log.js";

/**
 * Database connection configuration interface
 */
export interface DatabaseConfig {
    host: string;
    port: number;
    database: string;
    user: string;
    password: string;
}

/**
 * Parse DATABASE_URL or use fallback values
 * @param url - MySQL connection URL (e.g., mysql://user:password@host:port/database)
 * @returns Parsed database configuration object
 */
function parseDatabaseUrl(url: string): DatabaseConfig {
    try {
        const parsed = new URL(url);
        return {
            host: parsed.hostname,
            port: Number.parseInt(parsed.port) || 3306,
            database: parsed.pathname.slice(1), // Remove leading slash
            user: parsed.username,
            password: parsed.password,
        };
    } catch {
        LogErrorStack({ error: "Failed to parse database URL, using fallback values" });
        return {
            host: "************",
            port: 3306,
            database: "chikara",
            user: "root",
            password: "secret",
        };
    }
}

// Get database URL from environment or use fallback
const databaseUrl = process.env.DATABASE_URL;

// Parse the database configuration
export const dbConfig = parseDatabaseUrl(databaseUrl || "");

/**
 * Create a new PrismaMariaDb adapter with the configured database settings
 * @param connectionLimit - Maximum number of connections in the pool (default: 5)
 * @returns Configured PrismaMariaDb adapter instance
 */
export function createDatabaseAdapter(connectionLimit = 5): PrismaMariaDb {
    return new PrismaMariaDb({
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        user: dbConfig.user,
        password: dbConfig.password,
        connectionLimit,
    });
}

// Export a default adapter instance
export const defaultDatabaseAdapter = createDatabaseAdapter();
