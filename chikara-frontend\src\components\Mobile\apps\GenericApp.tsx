import React from 'react';
import { motion } from 'framer-motion';
import { MobileAppType } from '../types';
import { 
  Camera, 
  Calendar, 
  ShoppingBag, 
  Newspaper, 
  Gamepad2, 
  Trophy, 
  Home, 
  Swords, 
  GraduationCap, 
  Music 
} from 'lucide-react';

interface GenericAppProps {
  appType: MobileAppType;
}

const GenericApp: React.FC<GenericAppProps> = ({ appType }) => {
  const getAppContent = () => {
    switch (appType) {
      case 'camera':
        return {
          icon: Camera,
          title: 'Camera',
          description: 'Take photos and capture moments in Chikara Academy',
          color: 'bg-gray-600',
        };
      case 'calendar':
        return {
          icon: Calendar,
          title: 'Events',
          description: 'View upcoming events, tournaments, and gang activities',
          color: 'bg-red-500',
        };
      case 'shop':
        return {
          icon: ShoppingBag,
          title: 'Shop',
          description: 'Browse and purchase equipment, items, and upgrades',
          color: 'bg-orange-500',
        };
      case 'news':
        return {
          icon: Newspaper,
          title: 'News',
          description: 'Stay updated with the latest happenings in the city',
          color: 'bg-gray-700',
        };
      case 'arcade':
        return {
          icon: Gamepad2,
          title: 'Arcade',
          description: 'Play mini-games and earn rewards',
          color: 'bg-pink-500',
        };
      case 'rankings':
        return {
          icon: Trophy,
          title: 'Rankings',
          description: 'View leaderboards and your position among other warriors',
          color: 'bg-amber-500',
        };
      case 'property':
        return {
          icon: Home,
          title: 'Property',
          description: 'Manage your properties and real estate investments',
          color: 'bg-indigo-500',
        };
      case 'combat':
        return {
          icon: Swords,
          title: 'Combat',
          description: 'Challenge other players and engage in battles',
          color: 'bg-red-600',
        };
      case 'academy':
        return {
          icon: GraduationCap,
          title: 'Academy',
          description: 'Take classes, complete exams, and learn new skills',
          color: 'bg-blue-600',
        };
      case 'music':
        return {
          icon: Music,
          title: 'Music',
          description: 'Listen to the game soundtrack and ambient music',
          color: 'bg-purple-600',
        };
      default:
        return {
          icon: Gamepad2,
          title: 'App',
          description: 'This app is coming soon!',
          color: 'bg-gray-600',
        };
    }
  };

  const content = getAppContent();

  return (
    <div className="h-full bg-gray-900 flex flex-col items-center justify-center p-8">
      <motion.div
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: 'spring', damping: 20 }}
        className={`w-32 h-32 ${content.color} rounded-3xl flex items-center justify-center mb-6`}
      >
        <content.icon className="w-16 h-16 text-white" />
      </motion.div>
      
      <motion.h2
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="text-2xl font-bold text-white mb-3"
      >
        {content.title}
      </motion.h2>
      
      <motion.p
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="text-gray-400 text-center max-w-xs"
      >
        {content.description}
      </motion.p>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
        className="mt-8 bg-gray-800 rounded-lg p-4 text-center"
      >
        <p className="text-yellow-400 text-sm font-medium">Coming Soon!</p>
        <p className="text-gray-500 text-xs mt-1">This feature is under development</p>
      </motion.div>
    </div>
  );
};

export default GenericApp;
